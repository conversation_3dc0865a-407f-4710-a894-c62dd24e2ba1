#!/usr/bin/env python3
"""
gemini-balance 使用示例
展示如何通过你的gemini-balance服务调用Gemini模型
"""

import requests
import json
import time
from typing import Dict, List, Any

class GeminiBalanceClient:
    def __init__(self, base_url: str, auth_token: str):
        """
        初始化客户端
        
        Args:
            base_url: gemini-balance服务地址
            auth_token: 认证令牌
        """
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {auth_token}'
        }
    
    def chat_completion(self, messages: List[Dict], model: str = "gemini-1.5-flash", 
                       stream: bool = False, **kwargs) -> Dict[str, Any]:
        """
        聊天完成API调用
        
        Args:
            messages: 消息列表
            model: 模型名称
            stream: 是否流式响应
            **kwargs: 其他参数
        """
        url = f"{self.base_url}/v1/chat/completions"
        
        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            **kwargs
        }
        
        try:
            response = requests.post(url, headers=self.headers, json=data, timeout=30)
            response.raise_for_status()
            
            if stream:
                return self._handle_stream_response(response)
            else:
                return response.json()
                
        except requests.exceptions.RequestException as e:
            return {"error": f"请求失败: {e}"}
    
    def _handle_stream_response(self, response):
        """处理流式响应"""
        content = ""
        for line in response.iter_lines():
            if line:
                line = line.decode('utf-8')
                if line.startswith('data: '):
                    data = line[6:]
                    if data == '[DONE]':
                        break
                    try:
                        chunk = json.loads(data)
                        if 'choices' in chunk and chunk['choices']:
                            delta = chunk['choices'][0].get('delta', {})
                            if 'content' in delta:
                                content += delta['content']
                                print(delta['content'], end='', flush=True)
                    except json.JSONDecodeError:
                        continue
        return {"content": content}
    
    def get_models(self) -> Dict[str, Any]:
        """获取可用模型列表"""
        url = f"{self.base_url}/v1/models"
        
        try:
            response = requests.get(url, headers=self.headers, timeout=10)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            return {"error": f"获取模型列表失败: {e}"}

def main():
    """主函数 - 演示各种使用方式"""
    
    # 配置信息
    BASE_URL = "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    
    # 创建客户端
    client = GeminiBalanceClient(BASE_URL, AUTH_TOKEN)
    
    print("🚀 gemini-balance 使用示例")
    print("=" * 50)
    
    # 示例1: 获取模型列表
    print("\n📋 示例1: 获取可用模型")
    models = client.get_models()
    if "error" not in models:
        print("可用模型:")
        for model in models.get("data", []):
            print(f"  - {model.get('id', 'unknown')}")
    else:
        print(f"错误: {models['error']}")
    
    # 示例2: 基本聊天
    print("\n💬 示例2: 基本聊天")
    messages = [
        {"role": "user", "content": "你好！请简单介绍一下自己。"}
    ]
    
    response = client.chat_completion(messages, model="gemini-1.5-flash")
    if "error" not in response:
        content = response["choices"][0]["message"]["content"]
        print(f"AI回复: {content}")
    else:
        print(f"错误: {response['error']}")
    
    # 示例3: 流式聊天
    print("\n🌊 示例3: 流式聊天")
    messages = [
        {"role": "user", "content": "请写一首关于人工智能的短诗"}
    ]
    
    print("AI回复: ", end="")
    response = client.chat_completion(messages, model="gemini-1.5-flash", stream=True)
    print()  # 换行
    
    # 示例4: 多轮对话
    print("\n🔄 示例4: 多轮对话")
    conversation = [
        {"role": "user", "content": "我想学习Python编程"},
        {"role": "assistant", "content": "太好了！Python是一门很棒的编程语言。你想从哪里开始学习呢？"},
        {"role": "user", "content": "请推荐一些适合初学者的学习资源"}
    ]
    
    response = client.chat_completion(conversation, model="gemini-1.5-flash")
    if "error" not in response:
        content = response["choices"][0]["message"]["content"]
        print(f"AI回复: {content}")
    else:
        print(f"错误: {response['error']}")
    
    # 示例5: 带参数的调用
    print("\n⚙️ 示例5: 带参数的调用")
    messages = [
        {"role": "user", "content": "请用创意的方式描述一只猫"}
    ]
    
    response = client.chat_completion(
        messages, 
        model="gemini-1.5-flash",
        temperature=0.9,  # 提高创造性
        max_tokens=200    # 限制输出长度
    )
    
    if "error" not in response:
        content = response["choices"][0]["message"]["content"]
        print(f"AI回复: {content}")
    else:
        print(f"错误: {response['error']}")

def test_with_curl():
    """使用curl命令的示例"""
    print("\n🔧 curl 命令示例:")
    print("=" * 30)
    
    curl_example = '''
# 基本聊天请求
curl -X POST https://gemini-balance-9341.onrender.com/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer your-auth-token" \\
  -d '{
    "model": "gemini-1.5-flash",
    "messages": [
      {"role": "user", "content": "你好，世界！"}
    ]
  }'

# 流式请求
curl -X POST https://gemini-balance-9341.onrender.com/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer your-auth-token" \\
  -d '{
    "model": "gemini-1.5-flash",
    "messages": [
      {"role": "user", "content": "请写一个故事"}
    ],
    "stream": true
  }'

# 获取模型列表
curl -H "Authorization: Bearer your-auth-token" \\
  https://gemini-balance-9341.onrender.com/v1/models
'''
    print(curl_example)

def openai_sdk_example():
    """使用OpenAI SDK的示例"""
    print("\n🔌 OpenAI SDK 示例:")
    print("=" * 30)
    
    sdk_example = '''
# 安装: pip install openai

import openai

# 配置客户端
client = openai.OpenAI(
    api_key="your-auth-token",
    base_url="https://gemini-balance-9341.onrender.com/v1"
)

# 聊天完成
response = client.chat.completions.create(
    model="gemini-1.5-flash",
    messages=[
        {"role": "user", "content": "Hello, Gemini!"}
    ]
)

print(response.choices[0].message.content)

# 流式聊天
stream = client.chat.completions.create(
    model="gemini-1.5-flash",
    messages=[{"role": "user", "content": "Tell me a story"}],
    stream=True
)

for chunk in stream:
    if chunk.choices[0].delta.content is not None:
        print(chunk.choices[0].delta.content, end="")
'''
    print(sdk_example)

if __name__ == "__main__":
    print("⚠️  使用前请确保:")
    print("1. 替换 AUTH_TOKEN 为你的实际认证令牌")
    print("2. 确认 gemini-balance 服务正在运行")
    print("3. 检查网络连接")
    print()
    
    # 运行示例
    main()
    
    # 显示其他示例
    test_with_curl()
    openai_sdk_example()
    
    print("\n✅ 示例完成！")
    print("现在你可以根据这些示例开始使用 gemini-balance 了！")
