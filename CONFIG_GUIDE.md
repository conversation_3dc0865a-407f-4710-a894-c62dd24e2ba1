# 🚀 gemini-balance 环境配置指南

## 📋 配置文件说明

本项目提供了多种部署方案的环境配置模板，你可以根据需求选择合适的配置。

## 🎯 配置文件列表

### 📄 通用模板
- **`env_template.env`** - 完整的环境变量模板，包含所有可配置选项

### 🚂 Railway 部署
- **`config/railway/railway_native_config.env`** - Railway原生部署（推荐）
- **`config/railway/render_railway_config.env`** - Render + Railway MySQL

### 🎨 Render 部署
- **`config/render/render_sqlite_config.env`** - Render + SQLite（最简单）
- **`config/render/render_config.env`** - Render通用配置

### 🐘 Supabase 数据库
- **`config/supabase/supabase_config.env`** - Supabase PostgreSQL

### 🌟 PlanetScale 数据库
- **`config/planetscale/render_planetscale_config.env`** - PlanetScale MySQL

## 🎯 方案选择建议

### 1. 🥇 Railway 原生部署 (最推荐)
```bash
配置文件: config/railway/railway_native_config.env
```
**优势:**
- ✅ 数据库和应用在同一平台
- ✅ 内网连接，速度快，稳定
- ✅ 免费额度充足 ($5/月)
- ✅ 一键部署，配置简单

**适用场景:** 所有用户，特别是新手

### 2. 🥈 Render + SQLite (最简单)
```bash
配置文件: config/render/render_sqlite_config.env
```
**优势:**
- ✅ 无需外部数据库
- ✅ 配置最简单
- ✅ 完全免费
- ✅ 无网络连接问题

**适用场景:** 个人使用，低并发

### 3. 🥉 Supabase PostgreSQL (免费稳定)
```bash
配置文件: config/supabase/supabase_config.env
```
**优势:**
- ✅ 完全免费 (500MB)
- ✅ PostgreSQL功能强大
- ✅ 连接稳定
- ✅ 专业数据库服务

**适用场景:** 需要稳定数据库的用户

## 📝 使用步骤

### 步骤1: 选择配置文件
根据你的部署方案选择对应的配置文件

### 步骤2: 复制配置
```bash
cp config/railway/railway_native_config.env .env
```

### 步骤3: 修改配置
编辑 `.env` 文件，填入你的实际值：
```env
# 必须修改的配置
API_KEYS=["your-actual-gemini-api-key"]
AUTH_TOKEN=your-actual-auth-token

# 数据库配置（如果使用外部数据库）
MYSQL_PASSWORD=your-actual-password
```

### 步骤4: 部署应用
根据选择的平台进行部署

## 🔑 必填配置项

无论选择哪种方案，以下配置项都是必填的：

```env
# Gemini API密钥
API_KEYS=["your-gemini-api-key"]

# 访问令牌
AUTH_TOKEN=your-auth-token
```

## 🛠️ 可选配置项

### 功能开关
```env
TOOLS_CODE_EXECUTION_ENABLED=false    # 代码执行
URL_CONTEXT_ENABLED=false             # 网址上下文
STREAM_OPTIMIZER_ENABLED=false        # 流式优化
```

### 模型配置
```env
SEARCH_MODELS=["gemini-2.0-flash-exp"]
IMAGE_MODELS=["gemini-2.0-flash-exp"]
```

### 安全配置
```env
MAX_FAILURES=3
MAX_RETRIES=3
TIME_OUT=30
```

## 🚨 常见问题

### Q: 如何获取Gemini API密钥？
A: 访问 [Google AI Studio](https://makersuite.google.com/app/apikey) 创建API密钥

### Q: AUTH_TOKEN是什么？
A: 用于保护你的服务，可以设置任意字符串作为访问密码

### Q: 数据库连接失败怎么办？
A: 
1. 检查数据库配置是否正确
2. 尝试使用SQLite配置
3. 考虑切换到Railway原生部署

### Q: 如何启用高级功能？
A: 修改对应的功能开关为 `true`

## 📞 获取帮助

如果遇到配置问题：
1. 检查配置文件格式是否正确
2. 确认必填项已填写
3. 查看应用日志排查问题
4. 尝试使用更简单的配置方案
