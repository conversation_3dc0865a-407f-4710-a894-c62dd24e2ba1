# 📁 项目目录结构说明

本文档说明了 gemini-balance 项目的目录结构和文件组织方式。

## 🏗️ 目录结构

```
gemini-balance-main/
├── 📁 app/                          # 主应用代码
│   ├── config/                      # 应用配置
│   ├── core/                        # 核心功能
│   ├── database/                    # 数据库相关
│   ├── domain/                      # 业务领域
│   ├── exception/                   # 异常处理
│   ├── handler/                     # 请求处理器
│   ├── log/                         # 日志模块
│   ├── middleware/                  # 中间件
│   ├── router/                      # 路由配置
│   ├── scheduler/                   # 调度器
│   ├── service/                     # 业务服务
│   ├── static/                      # 静态文件
│   ├── templates/                   # 模板文件
│   ├── utils/                       # 工具函数
│   └── main.py                      # 应用入口
├── 📁 assets/                       # 静态资源
│   ├── dataocean.svg               # 图标文件
│   └── image*.png                  # 截图和图片
├── 📁 config/                       # 部署配置文件
│   ├── planetscale/                # PlanetScale 配置
│   │   └── render_planetscale_config.env
│   ├── railway/                    # Railway 配置
│   │   └── render_railway_config.env
│   └── render/                     # Render 配置
│       └── render_config.env
├── 📁 docs/                         # 文档
│   ├── deployment/                 # 部署文档
│   │   ├── ngrok_mysql_setup.md
│   │   ├── planetscale_setup.md
│   │   ├── railway_setup.md
│   │   ├── render_ngrok_config.md
│   │   ├── render_postgresql_setup.md
│   │   └── tencent_cloud_mysql_setup.md
│   └── alternative_solutions.md    # 替代方案文档
├── 📁 logs/                         # 日志文件
│   └── tunnel.log                  # 隧道日志
├── 📁 scripts/                      # 脚本文件
│   ├── database/                   # 数据库脚本
│   │   ├── mysql_http_proxy.py
│   │   └── mysql_http_proxy_server.py
│   ├── network/                    # 网络诊断脚本
│   │   ├── network_debug.py
│   │   └── network_diagnosis.sh
│   └── tunnel/                     # 隧道设置脚本
│       ├── setup_cloudflare_tunnel.sh
│       └── simple_tunnel_setup.sh
├── 📁 tests/                        # 测试文件
│   ├── database/                   # 数据库测试
│   │   ├── migrate_to_planetscale.py
│   │   ├── test_external_mysql.py
│   │   ├── test_local_mysql.py
│   │   ├── test_mysql_pymysql.py
│   │   ├── test_ngrok_mysql.py
│   │   ├── test_planetscale.py
│   │   ├── test_railway.py
│   │   └── test_railway_optimized.py
│   ├── network/                    # 网络测试
│   └── test_key_redaction.py       # 密钥脱敏测试
├── 📄 docker-compose.yml           # Docker Compose 配置
├── 📄 Dockerfile                   # Docker 镜像配置
├── 📄 requirements.txt             # Python 依赖
├── 📄 README.md                    # 项目说明
├── 📄 README_ZH.md                 # 中文说明
├── 📄 LICENSE                      # 许可证
└── 📄 VERSION                      # 版本号
```

## 📋 文件分类说明

### 🏠 核心应用 (`app/`)
- 包含所有应用程序代码
- 按功能模块组织（config, database, service等）
- 保持原有结构不变

### ⚙️ 配置文件 (`config/`)
- **render/**: Render 平台部署配置
- **railway/**: Railway 数据库配置  
- **planetscale/**: PlanetScale 数据库配置

### 📚 文档 (`docs/`)
- **deployment/**: 各种部署方案的详细说明
- **alternative_solutions.md**: 替代解决方案

### 🧪 测试文件 (`tests/`)
- **database/**: 数据库连接和迁移测试
- **network/**: 网络连接测试
- 包含各种云服务的连接测试脚本

### 🔧 脚本工具 (`scripts/`)
- **database/**: MySQL HTTP 代理脚本
- **network/**: 网络诊断工具
- **tunnel/**: 隧道设置脚本

### 🎨 静态资源 (`assets/`)
- 项目相关的图片和图标文件
- 文档中使用的截图

### 📝 日志文件 (`logs/`)
- 应用运行时产生的日志文件
- 隧道连接日志

## 🎯 使用指南

### 部署配置
1. 选择部署平台（Render/Railway/PlanetScale）
2. 使用对应的配置文件：`config/{platform}/`
3. 参考部署文档：`docs/deployment/`

### 数据库测试
1. 运行对应的测试脚本：`tests/database/test_{platform}.py`
2. 检查连接状态和性能

### 网络诊断
1. 使用网络诊断脚本：`scripts/network/`
2. 检查连接问题和延迟

### 隧道设置
1. 选择隧道方案：`scripts/tunnel/`
2. 按照脚本说明配置

## 📞 快速访问

- **开始部署**: `docs/deployment/`
- **测试连接**: `tests/database/`
- **解决问题**: `scripts/network/`
- **配置文件**: `config/`
