#!/bin/bash

# 简化的 Cloudflare Tunnel 设置
# 使用 trycloudflare.com 临时域名（无需自己的域名）

echo "🌐 简化 Cloudflare Tunnel 设置"
echo "============================="

# 检查 MySQL 容器
echo "1. 检查 MySQL 容器状态..."
if docker ps | grep -q mysql8-port443; then
    echo "✅ MySQL 容器运行中 (端口 443)"
else
    echo "❌ MySQL 容器未运行"
    echo "请先启动 MySQL 容器"
    exit 1
fi

# 配置 MySQL 用户（如果还没配置）
echo ""
echo "2. 配置 MySQL 用户..."
docker exec -i mysql8-port443 mysql -u root -p123qwe << 'EOF' 2>/dev/null
CREATE DATABASE IF NOT EXISTS gemini_balance CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER IF NOT EXISTS 'render_user'@'%' IDENTIFIED BY 'Strong_Password_123!';
GRANT ALL PRIVILEGES ON gemini_balance.* TO 'render_user'@'%';
FLUSH PRIVILEGES;
EOF

if [ $? -eq 0 ]; then
    echo "✅ MySQL 用户配置完成"
else
    echo "⚠️  MySQL 用户可能已存在"
fi

echo ""
echo "3. 🚀 启动 Cloudflare Tunnel..."
echo "   这将创建一个临时的公网地址"
echo "   按 Ctrl+C 停止隧道"
echo ""

# 启动隧道（使用临时域名）
cloudflared tunnel --url tcp://localhost:443

echo ""
echo "隧道已停止"
