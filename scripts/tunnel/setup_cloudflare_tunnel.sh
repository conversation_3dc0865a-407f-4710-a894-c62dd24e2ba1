#!/bin/bash

# Cloudflare Tunnel 设置脚本
# 为 MySQL 创建安全隧道

echo "🌐 设置 Cloudflare Tunnel for MySQL"
echo "=================================="

# 检查登录状态
echo "1. 检查 Cloudflare 登录状态..."
if cloudflared tunnel list > /dev/null 2>&1; then
    echo "✅ 已登录 Cloudflare"
else
    echo "❌ 未登录，请先运行: cloudflared tunnel login"
    exit 1
fi

# 创建隧道
TUNNEL_NAME="mysql-tunnel-$(date +%s)"
echo ""
echo "2. 创建隧道: $TUNNEL_NAME"
cloudflared tunnel create $TUNNEL_NAME

if [ $? -eq 0 ]; then
    echo "✅ 隧道创建成功"
else
    echo "❌ 隧道创建失败"
    exit 1
fi

# 获取隧道 ID
TUNNEL_ID=$(cloudflared tunnel list | grep $TUNNEL_NAME | awk '{print $1}')
echo "   隧道 ID: $TUNNEL_ID"

# 创建配置文件
CONFIG_DIR="$HOME/.cloudflared"
CONFIG_FILE="$CONFIG_DIR/config.yml"

echo ""
echo "3. 创建配置文件: $CONFIG_FILE"

# 确保目录存在
mkdir -p $CONFIG_DIR

# 写入配置
cat > $CONFIG_FILE << EOF
tunnel: $TUNNEL_ID
credentials-file: $CONFIG_DIR/$TUNNEL_ID.json

ingress:
  # MySQL 隧道 - 将本地 443 端口暴露为 TCP 服务
  - hostname: mysql.your-domain.com
    service: tcp://localhost:443
  # 默认规则（必需）
  - service: http_status:404
EOF

echo "✅ 配置文件已创建"
echo ""
echo "📋 配置内容:"
cat $CONFIG_FILE

echo ""
echo "4. 🚀 启动隧道..."
echo "   运行以下命令启动隧道:"
echo "   cloudflared tunnel run $TUNNEL_NAME"
echo ""
echo "5. 📝 Render 配置:"
echo "   MYSQL_HOST=mysql.your-domain.com"
echo "   MYSQL_PORT=443"
echo "   MYSQL_USER=render_user"
echo "   MYSQL_PASSWORD=Strong_Password_123!"
echo "   MYSQL_DATABASE=gemini_balance"
echo ""
echo "⚠️  注意: 你需要:"
echo "   1. 拥有一个域名"
echo "   2. 将域名 DNS 指向 Cloudflare"
echo "   3. 在 Cloudflare 中添加 mysql.your-domain.com 的 CNAME 记录"
