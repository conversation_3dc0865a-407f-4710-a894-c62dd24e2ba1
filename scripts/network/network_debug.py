#!/usr/bin/env python3
"""
网络连接调试脚本
帮助诊断为什么公网 IP 无法连接
"""

import socket
import subprocess
import sys
import time

def check_port_connectivity(host, port, timeout=5):
    """检查端口连通性"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except Exception as e:
        print(f"端口检查异常: {e}")
        return False

def run_command(cmd):
    """运行系统命令"""
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=10)
        return result.stdout.strip()
    except Exception as e:
        return f"命令执行失败: {e}"

def main():
    print("🔍 网络连接调试")
    print("=" * 60)
    
    # 测试目标
    targets = [
        ("本地回环", "127.0.0.1", 33060),
        ("内网 IP", "***********", 33060),
        ("公网 IP", "**************", 33060)
    ]
    
    print("\n1. 📡 端口连通性测试")
    print("-" * 40)
    for name, host, port in targets:
        print(f"测试 {name} ({host}:{port})...", end=" ")
        if check_port_connectivity(host, port):
            print("✅ 可达")
        else:
            print("❌ 不可达")
    
    print("\n2. 🐳 Docker 容器状态")
    print("-" * 40)
    docker_ps = run_command("docker ps | grep mysql")
    if docker_ps:
        print("✅ MySQL 容器运行中:")
        print(f"   {docker_ps}")
    else:
        print("❌ MySQL 容器未运行")
    
    print("\n3. 🔌 端口映射检查")
    print("-" * 40)
    port_mapping = run_command("docker port mysql8-public")
    if port_mapping:
        print("✅ 端口映射:")
        print(f"   {port_mapping}")
    else:
        print("❌ 无端口映射")
    
    print("\n4. 👂 端口监听状态")
    print("-" * 40)
    lsof_result = run_command("lsof -i :33060")
    if lsof_result and "LISTEN" in lsof_result:
        print("✅ 端口 33060 正在监听:")
        for line in lsof_result.split('\n'):
            if line.strip():
                print(f"   {line}")
    else:
        print("❌ 端口 33060 未监听")
    
    print("\n5. 🌐 网络接口")
    print("-" * 40)
    ifconfig = run_command("ifconfig | grep 'inet ' | grep -v '127.0.0.1'")
    if ifconfig:
        print("✅ 网络接口:")
        for line in ifconfig.split('\n'):
            if line.strip():
                print(f"   {line.strip()}")
    
    print("\n6. 🌍 公网 IP 确认")
    print("-" * 40)
    public_ip = run_command("curl -s -4 ifconfig.me")
    if public_ip:
        print(f"✅ 当前公网 IP: {public_ip}")
        if public_ip != "**************":
            print("⚠️  公网 IP 已变化！")
    else:
        print("❌ 无法获取公网 IP")
    
    print("\n7. 🔥 防火墙状态 (macOS)")
    print("-" * 40)
    firewall = run_command("sudo pfctl -s info 2>/dev/null || echo 'pfctl 需要 sudo 权限'")
    print(f"防火墙信息: {firewall}")
    
    print("\n8. 🧪 TCP 连接测试")
    print("-" * 40)
    print("使用 Python socket 测试各个地址...")
    
    for name, host, port in targets:
        print(f"测试 {name} ({host}:{port})...")
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            start_time = time.time()
            result = sock.connect_ex((host, port))
            end_time = time.time()
            sock.close()
            
            if result == 0:
                print(f"   ✅ 连接成功 (耗时: {end_time - start_time:.2f}s)")
            else:
                print(f"   ❌ 连接失败 (错误码: {result})")
        except Exception as e:
            print(f"   ❌ 异常: {e}")
    
    print("\n" + "=" * 60)
    print("🎯 诊断总结:")
    print("1. 如果本地连接成功但公网连接失败，可能是:")
    print("   - 路由器/防火墙阻止了外部访问")
    print("   - ISP 阻止了端口 33060")
    print("   - NAT 配置问题")
    print("2. 建议解决方案:")
    print("   - 检查路由器端口转发设置")
    print("   - 尝试其他端口 (如 8080, 8443)")
    print("   - 使用 ngrok 或 Cloudflare Tunnel")
    print("   - 考虑使用 Render 的 PostgreSQL")

if __name__ == "__main__":
    main()
