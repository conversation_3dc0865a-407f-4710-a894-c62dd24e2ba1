#!/bin/bash

# 网络诊断脚本 - 帮助确定 MySQL 连接的最佳方案

echo "🔍 MySQL 容器网络诊断"
echo "======================="

# 1. 检查容器状态
echo "1. 📦 检查 MySQL 容器状态..."
if docker ps | grep -q mysql8-public; then
    echo "   ✅ MySQL 容器正在运行"
    CONTAINER_IP=$(docker inspect mysql8-public --format='{{range .NetworkSettings.Networks}}{{.IPAddress}}{{end}}')
    echo "   📍 容器 IP: $CONTAINER_IP"
else
    echo "   ❌ MySQL 容器未运行"
    exit 1
fi

# 2. 检查端口映射
echo ""
echo "2. 🔌 检查端口映射..."
PORT_MAPPING=$(docker port mysql8-public)
echo "   $PORT_MAPPING"

# 3. 检查本机网络接口
echo ""
echo "3. 🌐 检查本机网络接口..."
LOCAL_IPS=$(ifconfig | grep "inet " | grep -v "127.0.0.1" | awk '{print $2}')
echo "   本机 IP 地址:"
for ip in $LOCAL_IPS; do
    echo "   - $ip"
done

# 4. 检查公网 IP
echo ""
echo "4. 🌍 检查公网 IP..."
PUBLIC_IP=$(curl -s -4 ifconfig.me)
echo "   公网 IP: $PUBLIC_IP"

# 5. 测试本地连接
echo ""
echo "5. 🧪 测试本地连接..."
if docker exec mysql8-public mysql -u render_user -p'Strong_Password_123!' -e 'SELECT "Local connection OK" as status;' 2>/dev/null; then
    echo "   ✅ 本地连接成功"
else
    echo "   ❌ 本地连接失败"
fi

# 6. 检查端口监听状态
echo ""
echo "6. 👂 检查端口监听状态..."
if netstat -an | grep -q ":33060.*LISTEN"; then
    echo "   ✅ 端口 33060 正在监听"
    netstat -an | grep ":33060"
else
    echo "   ❌ 端口 33060 未监听"
fi

# 7. 建议配置
echo ""
echo "7. 💡 Render 配置建议..."
echo "   基于以上检查，建议在 Render 中使用以下配置："
echo ""
echo "   MYSQL_HOST=$PUBLIC_IP"
echo "   MYSQL_PORT=33060"
echo "   MYSQL_USER=render_user"
echo "   MYSQL_PASSWORD=Strong_Password_123!"
echo "   MYSQL_DATABASE=gemini_balance"
echo ""

# 8. 故障排除建议
echo "8. 🔧 如果 Render 连接失败，请尝试："
echo "   1. 检查路由器防火墙设置，开放 33060 端口"
echo "   2. 联系 ISP 确认端口是否被阻止"
echo "   3. 考虑使用 ngrok 或 Cloudflare Tunnel"
echo "   4. 或者使用 Render 的 PostgreSQL 数据库"
echo ""
echo "🎯 诊断完成！"
