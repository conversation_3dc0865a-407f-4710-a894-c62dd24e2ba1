#!/usr/bin/env python3
"""
MySQL HTTP 代理服务器
将 HTTP 请求转换为 MySQL 连接，用于通过 Cloudflare HTTP 隧道访问
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import pymysql
import urllib.parse

class MySQLProxyHandler(BaseHTTPRequestHandler):
    def do_POST(self):
        """处理 POST 请求，执行 MySQL 查询"""
        try:
            # 读取请求数据
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            # MySQL 连接配置
            config = {
                'host': '127.0.0.1',
                'port': 3306,
                'user': 'render_user',
                'password': 'Strong_Password_123!',
                'database': 'gemini_balance',
                'charset': 'utf8mb4'
            }
            
            # 执行查询
            connection = pymysql.connect(**config)
            with connection.cursor() as cursor:
                cursor.execute(data.get('query', 'SELECT 1'))
                if data.get('query', '').strip().upper().startswith('SELECT'):
                    result = cursor.fetchall()
                else:
                    connection.commit()
                    result = {'affected_rows': cursor.rowcount}
            
            connection.close()
            
            # 返回结果
            response = {
                'status': 'success',
                'data': result
            }
            
            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))
            
        except Exception as e:
            # 返回错误
            response = {
                'status': 'error',
                'message': str(e)
            }
            
            self.send_response(500)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def do_GET(self):
        """处理 GET 请求，返回状态信息"""
        if self.path == '/health':
            try:
                # 测试 MySQL 连接
                config = {
                    'host': '127.0.0.1',
                    'port': 443,
                    'user': 'render_user',
                    'password': 'Strong_Password_123!',
                    'database': 'gemini_balance',
                    'charset': 'utf8mb4'
                }
                
                connection = pymysql.connect(**config)
                connection.close()
                
                response = {
                    'status': 'healthy',
                    'mysql': 'connected'
                }
                status_code = 200
                
            except Exception as e:
                response = {
                    'status': 'unhealthy',
                    'mysql': str(e)
                }
                status_code = 500
            
            self.send_response(status_code)
            self.send_header('Content-type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))
        else:
            self.send_response(404)
            self.end_headers()

def run_proxy_server(port=8000):
    """启动代理服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, MySQLProxyHandler)
    print(f"🚀 MySQL HTTP 代理服务器启动在端口 {port}")
    print(f"   健康检查: http://localhost:{port}/health")
    print(f"   MySQL 查询: POST http://localhost:{port}/ (JSON: {'query': 'SELECT 1'})")
    print("   按 Ctrl+C 停止服务器")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == "__main__":
    run_proxy_server()
