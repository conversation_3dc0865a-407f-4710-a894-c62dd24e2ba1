#!/usr/bin/env python3
"""
MySQL HTTP 代理服务器
通过 HTTP API 访问 MySQL，配合 ngrok HTTP 隧道使用（免费）
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import json
import pymysql
import threading
import time
from datetime import datetime

class MySQLHTTPProxy(BaseHTTPRequestHandler):
    
    def do_OPTIONS(self):
        """处理 CORS 预检请求"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def do_GET(self):
        """处理 GET 请求"""
        if self.path == '/health':
            self.health_check()
        elif self.path == '/status':
            self.mysql_status()
        else:
            self.send_error(404, "Not Found")
    
    def do_POST(self):
        """处理 POST 请求 - 执行 MySQL 查询"""
        if self.path == '/query':
            self.execute_query()
        elif self.path == '/batch':
            self.execute_batch()
        else:
            self.send_error(404, "Not Found")
    
    def health_check(self):
        """健康检查"""
        try:
            conn = self.get_mysql_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            conn.close()
            
            response = {
                'status': 'healthy',
                'mysql': 'connected',
                'timestamp': datetime.now().isoformat()
            }
            self.send_json_response(response, 200)
            
        except Exception as e:
            response = {
                'status': 'unhealthy',
                'mysql': str(e),
                'timestamp': datetime.now().isoformat()
            }
            self.send_json_response(response, 500)
    
    def mysql_status(self):
        """MySQL 状态信息"""
        try:
            conn = self.get_mysql_connection()
            with conn.cursor() as cursor:
                cursor.execute("SELECT VERSION() as version, NOW() as current_time, DATABASE() as current_db")
                result = cursor.fetchone()
                
                cursor.execute("SHOW STATUS LIKE 'Threads_connected'")
                threads = cursor.fetchone()
                
            conn.close()
            
            response = {
                'mysql_version': result[0],
                'server_time': result[1].isoformat() if result[1] else None,
                'current_database': result[2],
                'connections': threads[1] if threads else 'unknown',
                'proxy_status': 'running'
            }
            self.send_json_response(response, 200)
            
        except Exception as e:
            response = {
                'error': str(e),
                'proxy_status': 'error'
            }
            self.send_json_response(response, 500)
    
    def execute_query(self):
        """执行单个 SQL 查询"""
        try:
            # 读取请求数据
            content_length = int(self.headers.get('Content-Length', 0))
            if content_length == 0:
                self.send_error(400, "Empty request body")
                return
                
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            query = data.get('query', '').strip()
            params = data.get('params', [])
            
            if not query:
                self.send_error(400, "Missing query")
                return
            
            # 执行查询
            conn = self.get_mysql_connection()
            with conn.cursor() as cursor:
                cursor.execute(query, params)
                
                if query.upper().startswith('SELECT') or query.upper().startswith('SHOW'):
                    # 查询操作
                    results = cursor.fetchall()
                    columns = [desc[0] for desc in cursor.description] if cursor.description else []
                    
                    response = {
                        'success': True,
                        'data': results,
                        'columns': columns,
                        'row_count': len(results)
                    }
                else:
                    # 修改操作
                    conn.commit()
                    response = {
                        'success': True,
                        'affected_rows': cursor.rowcount,
                        'last_insert_id': cursor.lastrowid
                    }
            
            conn.close()
            self.send_json_response(response, 200)
            
        except json.JSONDecodeError:
            self.send_error(400, "Invalid JSON")
        except Exception as e:
            response = {
                'success': False,
                'error': str(e)
            }
            self.send_json_response(response, 500)
    
    def execute_batch(self):
        """执行批量 SQL 查询"""
        try:
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            queries = data.get('queries', [])
            if not queries:
                self.send_error(400, "Missing queries")
                return
            
            results = []
            conn = self.get_mysql_connection()
            
            try:
                for query_data in queries:
                    query = query_data.get('query', '').strip()
                    params = query_data.get('params', [])
                    
                    with conn.cursor() as cursor:
                        cursor.execute(query, params)
                        
                        if query.upper().startswith('SELECT') or query.upper().startswith('SHOW'):
                            data = cursor.fetchall()
                            columns = [desc[0] for desc in cursor.description] if cursor.description else []
                            results.append({
                                'success': True,
                                'data': data,
                                'columns': columns
                            })
                        else:
                            results.append({
                                'success': True,
                                'affected_rows': cursor.rowcount
                            })
                
                conn.commit()
                
            except Exception as e:
                conn.rollback()
                raise e
            finally:
                conn.close()
            
            response = {
                'success': True,
                'results': results
            }
            self.send_json_response(response, 200)
            
        except Exception as e:
            response = {
                'success': False,
                'error': str(e)
            }
            self.send_json_response(response, 500)
    
    def get_mysql_connection(self):
        """获取 MySQL 连接"""
        return pymysql.connect(
            host='127.0.0.1',
            port=443,
            user='render_user',
            password='Strong_Password_123!',
            database='gemini_balance',
            charset='utf8mb4',
            autocommit=False
        )
    
    def send_json_response(self, data, status_code=200):
        """发送 JSON 响应"""
        response_data = json.dumps(data, ensure_ascii=False, default=str)
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Content-Length', str(len(response_data.encode('utf-8'))))
        self.end_headers()
        self.wfile.write(response_data.encode('utf-8'))

def run_server(port=8000):
    """启动 HTTP 代理服务器"""
    server_address = ('', port)
    httpd = HTTPServer(server_address, MySQLHTTPProxy)
    
    print(f"🚀 MySQL HTTP 代理服务器启动")
    print(f"   地址: http://localhost:{port}")
    print(f"   健康检查: GET /health")
    print(f"   MySQL 状态: GET /status")
    print(f"   执行查询: POST /query")
    print(f"   批量查询: POST /batch")
    print(f"   按 Ctrl+C 停止服务器")
    print()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
        httpd.server_close()

if __name__ == "__main__":
    run_server()
