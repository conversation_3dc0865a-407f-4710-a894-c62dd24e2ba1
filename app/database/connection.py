"""
数据库连接池模块
"""
from pathlib import Path
from urllib.parse import quote_plus
from databases import Database
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base

from app.config.config import settings
from app.log.logger import get_database_logger

logger = get_database_logger()

# 数据库URL
if settings.DATABASE_TYPE == "sqlite":
    # 确保 data 目录存在
    data_dir = Path("data")
    data_dir.mkdir(exist_ok=True)
    db_path = data_dir / settings.SQLITE_DATABASE
    DATABASE_URL = f"sqlite:///{db_path}"
elif settings.DATABASE_TYPE == "mysql":
    if settings.MYSQL_SOCKET:
        DATABASE_URL = f"mysql+pymysql://{settings.MYSQL_USER}:{quote_plus(settings.MYSQL_PASSWORD)}@/{settings.MYSQL_DATABASE}?unix_socket={settings.MYSQL_SOCKET}"
    else:
        # 构建基础 URL
        DATABASE_URL = f"mysql+pymysql://{settings.MYSQL_USER}:{quote_plus(settings.MYSQL_PASSWORD)}@{settings.MYSQL_HOST}:{settings.MYSQL_PORT}/{settings.MYSQL_DATABASE}"

        # 添加连接参数
        params = []

        # SSL 配置
        if hasattr(settings, 'MYSQL_SSL_DISABLED') and not settings.MYSQL_SSL_DISABLED:
            params.append("ssl_disabled=false")
        if "psdb.cloud" in settings.MYSQL_HOST:  # PlanetScale 特殊处理
            params.append("ssl_disabled=false")
            params.append("ssl_verify_cert=true")

        # 云平台连接优化参数
        if "rlwy.net" in settings.MYSQL_HOST:  # Railway 特殊处理
            params.extend([
                "connect_timeout=30",      # 连接超时30秒
                "read_timeout=30",         # 读取超时30秒
                "write_timeout=30",        # 写入超时30秒
                "autocommit=true",         # 自动提交
                "charset=utf8mb4"          # 字符集
            ])

        if params:
            DATABASE_URL += "?" + "&".join(params)
else:
    raise ValueError("Unsupported database type. Please set DATABASE_TYPE to 'sqlite' or 'mysql'.")

# 创建数据库引擎
# pool_pre_ping=True: 在从连接池获取连接前执行简单的 "ping" 测试，确保连接有效
# 为云平台连接优化引擎参数
engine_kwargs = {
    "pool_pre_ping": True,
    "pool_recycle": 1800,  # 30分钟回收连接
}

# Railway 特殊优化
if settings.DATABASE_TYPE == "mysql" and "rlwy.net" in settings.MYSQL_HOST:
    engine_kwargs.update({
        "pool_timeout": 30,        # 获取连接超时
        "max_overflow": 10,        # 最大溢出连接数
        "pool_size": 5,            # 连接池大小
    })

engine = create_engine(DATABASE_URL, **engine_kwargs)

# 创建元数据对象
metadata = MetaData()

# 创建基类
Base = declarative_base(metadata=metadata)

# 创建数据库连接池，并配置连接池参数
if settings.DATABASE_TYPE == "sqlite":
    database = Database(DATABASE_URL)
else:
    # 为云平台连接优化参数
    db_kwargs = {
        "min_size": 2,           # 最小连接数（降低以减少资源使用）
        "max_size": 10,          # 最大连接数（降低以避免连接过多）
        "pool_recycle": 1800,    # 30分钟回收连接
    }

    # Railway 特殊优化
    if "rlwy.net" in settings.MYSQL_HOST:
        db_kwargs.update({
            "min_size": 1,       # Railway连接较慢，减少最小连接数
            "max_size": 5,       # 减少最大连接数
            "pool_recycle": 900, # 15分钟回收连接
        })

    database = Database(DATABASE_URL, **db_kwargs)

async def connect_to_db():
    """
    连接到数据库，带重试机制
    """
    import asyncio

    max_retries = 3
    retry_delay = 5  # 秒

    for attempt in range(max_retries):
        try:
            await database.connect()
            logger.info(f"Connected to {settings.DATABASE_TYPE} (attempt {attempt + 1})")
            return
        except Exception as e:
            logger.warning(f"Database connection attempt {attempt + 1} failed: {str(e)}")

            if attempt < max_retries - 1:
                logger.info(f"Retrying in {retry_delay} seconds...")
                await asyncio.sleep(retry_delay)
                retry_delay *= 2  # 指数退避
            else:
                logger.error(f"Failed to connect to database after {max_retries} attempts")
                raise


async def disconnect_from_db():
    """
    断开数据库连接
    """
    try:
        await database.disconnect()
        logger.info(f"Disconnected from {settings.DATABASE_TYPE}")
    except Exception as e:
        logger.error(f"Failed to disconnect from database: {str(e)}")
