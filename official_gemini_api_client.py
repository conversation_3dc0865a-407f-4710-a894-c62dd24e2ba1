#!/usr/bin/env python3
"""
使用 Google 官方 Gemini API 的客户端
API 地址: https://generativelanguage.googleapis.com/v1beta
"""

import requests
import json
import time
import urllib3
from typing import Dict, List, Any, Optional

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class OfficialGeminiClient:
    def __init__(self, api_key: str):
        """
        初始化官方 Gemini API 客户端
        
        Args:
            api_key: Google AI Studio 的 API Key
        """
        self.api_key = api_key
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建配置好的requests session"""
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'User-Agent': 'OfficialGeminiClient/1.0'
        })
        return session
    
    def get_models(self) -> Dict[str, Any]:
        """
        获取可用模型列表
        GET /v1beta/models
        """
        url = f"{self.base_url}/models?key={self.api_key}"
        
        try:
            print(f"🔍 获取模型列表...")
            response = self.session.get(url, timeout=(10, 30))
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            return {"error": f"请求失败: {e}"}
    
    def generate_content(self, model_name: str, contents: List[Dict], 
                        generation_config: Optional[Dict] = None,
                        safety_settings: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        生成内容 (非流式)
        POST /v1beta/models/{model_name}:generateContent
        
        Args:
            model_name: 模型名称，如 "gemini-1.5-flash"
            contents: 内容列表，格式为 [{"parts": [{"text": "..."}]}]
            generation_config: 生成配置
            safety_settings: 安全设置
        """
        url = f"{self.base_url}/models/{model_name}:generateContent?key={self.api_key}"
        
        data = {
            "contents": contents
        }
        
        if generation_config:
            data["generationConfig"] = generation_config
            
        if safety_settings:
            data["safetySettings"] = safety_settings
        
        try:
            print(f"🤖 调用模型: {model_name}")
            response = self.session.post(url, json=data, timeout=(15, 120))
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            return {"error": f"请求失败: {e}"}
    
    def stream_generate_content(self, model_name: str, contents: List[Dict],
                               generation_config: Optional[Dict] = None,
                               safety_settings: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        流式生成内容
        POST /v1beta/models/{model_name}:streamGenerateContent
        
        Args:
            model_name: 模型名称
            contents: 内容列表
            generation_config: 生成配置
            safety_settings: 安全设置
        """
        url = f"{self.base_url}/models/{model_name}:streamGenerateContent?key={self.api_key}"
        
        data = {
            "contents": contents
        }
        
        if generation_config:
            data["generationConfig"] = generation_config
            
        if safety_settings:
            data["safetySettings"] = safety_settings
        
        try:
            print(f"🌊 流式调用模型: {model_name}")
            response = self.session.post(url, json=data, timeout=(15, 120), stream=True)
            
            if response.status_code == 200:
                return self._handle_stream_response(response)
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            return {"error": f"请求失败: {e}"}
    
    def _handle_stream_response(self, response):
        """处理流式响应"""
        full_content = ""
        
        try:
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    # 官方API返回的是JSON数组格式
                    if line.startswith('[') or line.startswith(','):
                        # 移除开头的 [ 或 ,
                        json_str = line.lstrip('[,').rstrip(',')
                        if json_str:
                            try:
                                chunk = json.loads(json_str)
                                if 'candidates' in chunk:
                                    for candidate in chunk['candidates']:
                                        if 'content' in candidate and 'parts' in candidate['content']:
                                            for part in candidate['content']['parts']:
                                                if 'text' in part:
                                                    text = part['text']
                                                    full_content += text
                                                    print(text, end='', flush=True)
                            except json.JSONDecodeError:
                                continue
            
            return {"content": full_content}
            
        except Exception as e:
            return {"error": f"流式响应处理失败: {e}"}

def main():
    """主函数 - 演示官方 Gemini API 调用"""
    
    # 配置信息 - 请替换为你的实际 API Key
    API_KEY = "YOUR_GOOGLE_AI_STUDIO_API_KEY"  # 请替换为实际的API Key
    
    if API_KEY == "YOUR_GOOGLE_AI_STUDIO_API_KEY":
        print("❌ 请先设置你的 Google AI Studio API Key")
        print("1. 访问 https://aistudio.google.com/app/apikey")
        print("2. 创建 API Key")
        print("3. 替换代码中的 API_KEY 变量")
        return
    
    print("🚀 Google 官方 Gemini API 调用测试")
    print("=" * 50)
    print(f"API 地址: https://generativelanguage.googleapis.com/v1beta")
    print(f"API Key: {API_KEY[:10]}...")
    print()
    
    # 创建客户端
    client = OfficialGeminiClient(API_KEY)
    
    # 1. 获取模型列表
    print("📋 1. 获取可用模型列表")
    print("-" * 30)
    models = client.get_models()
    
    if "error" not in models:
        print("✅ 获取成功!")
        if "models" in models:
            print("可用模型:")
            for model in models["models"]:
                model_name = model.get("name", "unknown")
                display_name = model.get("displayName", "")
                supported_methods = model.get("supportedGenerationMethods", [])
                print(f"  - {model_name}")
                print(f"    显示名: {display_name}")
                print(f"    支持方法: {', '.join(supported_methods)}")
                print()
        else:
            print("模型列表格式:", json.dumps(models, indent=2, ensure_ascii=False)[:500])
    else:
        print(f"❌ 获取失败: {models['error']}")
    
    # 2. 测试内容生成 (非流式)
    print("💬 2. 测试内容生成 (非流式)")
    print("-" * 30)
    
    contents = [
        {
            "parts": [
                {"text": "你好！请简单介绍一下你自己，并说明你的版本和能力。"}
            ]
        }
    ]
    
    generation_config = {
        "temperature": 0.7,
        "maxOutputTokens": 200,
        "topP": 0.8,
        "topK": 40
    }
    
    # 安全设置 - 设置为较宽松的级别
    safety_settings = [
        {
            "category": "HARM_CATEGORY_HARASSMENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_HATE_SPEECH", 
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        },
        {
            "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
            "threshold": "BLOCK_MEDIUM_AND_ABOVE"
        }
    ]
    
    response = client.generate_content(
        model_name="gemini-1.5-flash",
        contents=contents,
        generation_config=generation_config,
        safety_settings=safety_settings
    )
    
    if "error" not in response:
        print("✅ 生成成功!")
        if "candidates" in response:
            for candidate in response["candidates"]:
                if "content" in candidate and "parts" in candidate["content"]:
                    for part in candidate["content"]["parts"]:
                        if "text" in part:
                            print(f"AI回复: {part['text']}")
        else:
            print("响应格式:", json.dumps(response, indent=2, ensure_ascii=False)[:300])
    else:
        print(f"❌ 生成失败: {response['error']}")
    
    print()
    
    # 3. 测试流式内容生成
    print("🌊 3. 测试流式内容生成")
    print("-" * 30)
    
    stream_contents = [
        {
            "parts": [
                {"text": "请写一首关于人工智能的短诗，要有创意和想象力。"}
            ]
        }
    ]
    
    print("AI回复: ", end="")
    stream_response = client.stream_generate_content(
        model_name="gemini-1.5-flash",
        contents=stream_contents,
        generation_config={"temperature": 0.9, "maxOutputTokens": 300},
        safety_settings=safety_settings
    )
    
    print()  # 换行
    
    if "error" in stream_response:
        print(f"❌ 流式生成失败: {stream_response['error']}")
    else:
        print("✅ 流式生成完成!")

def show_curl_examples():
    """显示 curl 命令示例"""
    print("\n🔧 等效的 curl 命令示例")
    print("=" * 40)
    
    api_key = "YOUR_API_KEY"
    
    examples = f'''
# 1. 获取模型列表
curl "https://generativelanguage.googleapis.com/v1beta/models?key={api_key}"

# 2. 生成内容 (非流式)
curl -X POST "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}" \\
  -H "Content-Type: application/json" \\
  -d '{{
    "contents": [
      {{
        "parts": [
          {{"text": "你好，世界！"}}
        ]
      }}
    ],
    "generationConfig": {{
      "temperature": 0.7,
      "maxOutputTokens": 200
    }}
  }}'

# 3. 流式生成内容
curl -X POST "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:streamGenerateContent?key={api_key}" \\
  -H "Content-Type: application/json" \\
  -d '{{
    "contents": [
      {{
        "parts": [
          {{"text": "请写一个故事"}}
        ]
      }}
    ]
  }}'
'''
    print(examples)

if __name__ == "__main__":
    main()
    show_curl_examples()
    
    print("\n" + "=" * 50)
    print("📝 Google 官方 Gemini API 总结:")
    print("✅ 直接使用 Google 官方 API")
    print("✅ 需要 Google AI Studio API Key")
    print("✅ 支持完整的 Gemini 功能")
    print("✅ 稳定性和可靠性最高")
    print("\n🔗 相关链接:")
    print("- Google AI Studio: https://aistudio.google.com/")
    print("- API 文档: https://ai.google.dev/docs")
    print("- 获取 API Key: https://aistudio.google.com/app/apikey")
