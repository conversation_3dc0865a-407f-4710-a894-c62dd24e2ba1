# 🚀 gemini-balance 环境配置模板
# 复制此文件并根据你的部署环境填入相应的值

# ==========================================
# 🗄️ 数据库配置 (必填)
# ==========================================

# 数据库类型: sqlite 或 mysql 或 postgresql
DATABASE_TYPE=mysql

# SQLite 配置 (如果使用 SQLite)
SQLITE_DATABASE=gemini_balance.db

# MySQL 配置 (如果使用 MySQL)
MYSQL_HOST=your-mysql-host
MYSQL_PORT=3306
MYSQL_USER=your-mysql-user
MYSQL_PASSWORD=your-mysql-password
MYSQL_DATABASE=your-mysql-database
MYSQL_SOCKET=
MYSQL_SSL_DISABLED=true

# PostgreSQL 配置 (如果使用 PostgreSQL)
POSTGRES_HOST=your-postgres-host
POSTGRES_PORT=5432
POSTGRES_USER=your-postgres-user
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_DATABASE=your-postgres-database

# ==========================================
# 🔑 API 配置 (必填)
# ==========================================

# Gemini API Keys (JSON数组格式)
API_KEYS=["your-gemini-api-key-1","your-gemini-api-key-2"]

# 允许的访问令牌 (JSON数组格式)
ALLOWED_TOKENS=["your-access-token-1","your-access-token-2"]

# 认证令牌 (如果不设置，会使用第一个ALLOWED_TOKENS)
AUTH_TOKEN=your-auth-token

# Gemini API 基础URL
BASE_URL=https://generativelanguage.googleapis.com/v1beta

# Vertex AI 配置 (可选)
VERTEX_API_KEYS=[]
VERTEX_EXPRESS_BASE_URL=https://aiplatform.googleapis.com/v1beta1/publishers/google

# ==========================================
# ⚙️ 基础配置
# ==========================================

# 日志级别: DEBUG, INFO, WARNING, ERROR
LOG_LEVEL=INFO

# 最大失败次数
MAX_FAILURES=3

# 最大重试次数
MAX_RETRIES=3

# 请求超时时间 (秒)
TIME_OUT=30

# 测试模型
TEST_MODEL=gemini-1.5-flash

# ==========================================
# 🌐 网络配置 (可选)
# ==========================================

# 代理服务器 (JSON数组格式)
PROXIES=[]

# 是否使用一致性哈希选择代理
PROXIES_USE_CONSISTENCY_HASH_BY_API_KEY=true

# 自定义请求头 (JSON对象格式)
CUSTOM_HEADERS={}

# ==========================================
# 🤖 模型配置 (可选)
# ==========================================

# 搜索模型
SEARCH_MODELS=["gemini-2.0-flash-exp"]

# 图像模型
IMAGE_MODELS=["gemini-2.0-flash-exp"]

# 过滤的模型
FILTERED_MODELS=[]

# 思考模型
THINKING_MODELS=[]

# 思考预算映射 (JSON对象格式)
THINKING_BUDGET_MAP={}

# ==========================================
# 🎨 功能开关 (可选)
# ==========================================

# 是否启用智能路由映射
URL_NORMALIZATION_ENABLED=false

# 是否启用代码执行工具
TOOLS_CODE_EXECUTION_ENABLED=false

# 是否启用网址上下文
URL_CONTEXT_ENABLED=false

# 网址上下文支持的模型
URL_CONTEXT_MODELS=["gemini-2.5-pro","gemini-2.5-flash"]

# 是否显示搜索链接
SHOW_SEARCH_LINK=true

# 是否显示思考过程
SHOW_THINKING_PROCESS=true

# ==========================================
# 🔊 TTS 配置 (可选)
# ==========================================

# TTS 模型
TTS_MODEL=gemini-2.5-flash-preview-tts

# TTS 语音名称
TTS_VOICE_NAME=Zephyr

# TTS 语速: slow, normal, fast
TTS_SPEED=normal

# ==========================================
# 🎨 图像生成配置 (可选)
# ==========================================

# 付费密钥 (用于图像生成)
PAID_KEY=

# 图像生成模型
CREATE_IMAGE_MODEL=imagen-3.0-generate-001

# 图片上传服务商: smms, picgo, cloudflare
UPLOAD_PROVIDER=smms

# SM.MS 配置
SMMS_SECRET_TOKEN=

# PicGo 配置
PICGO_API_KEY=

# Cloudflare 图床配置
CLOUDFLARE_IMGBED_URL=
CLOUDFLARE_IMGBED_AUTH_CODE=
CLOUDFLARE_IMGBED_UPLOAD_FOLDER=

# ==========================================
# 📡 流式输出配置 (可选)
# ==========================================

# 是否启用流式输出优化器
STREAM_OPTIMIZER_ENABLED=false

# 流式输出延迟配置
STREAM_MIN_DELAY=0.01
STREAM_MAX_DELAY=0.05
STREAM_SHORT_TEXT_THRESHOLD=50
STREAM_LONG_TEXT_THRESHOLD=200
STREAM_CHUNK_SIZE=10

# 假流式配置
FAKE_STREAM_ENABLED=false
FAKE_STREAM_EMPTY_DATA_INTERVAL_SECONDS=5

# ==========================================
# ⏰ 调度器配置 (可选)
# ==========================================

# 检查间隔 (小时)
CHECK_INTERVAL_HOURS=1

# 时区
TIMEZONE=Asia/Shanghai

# ==========================================
# 📝 日志管理 (可选)
# ==========================================

# 是否自动删除错误日志
AUTO_DELETE_ERROR_LOGS_ENABLED=true
AUTO_DELETE_ERROR_LOGS_DAYS=7

# 是否自动删除请求日志
AUTO_DELETE_REQUEST_LOGS_ENABLED=false
AUTO_DELETE_REQUEST_LOGS_DAYS=30

# ==========================================
# 📁 文件管理 (可选)
# ==========================================

# 是否启用文件清理
FILES_CLEANUP_ENABLED=true
FILES_CLEANUP_INTERVAL_HOURS=1

# 是否启用用户文件隔离
FILES_USER_ISOLATION_ENABLED=true

# ==========================================
# 👨‍💼 管理员配置 (可选)
# ==========================================

# 管理员会话过期时间 (秒, 5分钟到24小时)
ADMIN_SESSION_EXPIRE=3600

# ==========================================
# 🔒 安全配置 (可选)
# ==========================================

# 安全设置 (JSON数组格式)
SAFETY_SETTINGS=[
  {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
  {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
  {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
  {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"}
]

# ==========================================
# 📊 GitHub 配置 (可选)
# ==========================================

# GitHub 仓库信息 (用于更新检查)
GITHUB_REPO_OWNER=snailyp
GITHUB_REPO_NAME=gemini-balance
