# 🐘 Supabase PostgreSQL 免费方案

## 为什么选择Supabase？

- 🆓 **完全免费**: 500MB数据库，足够使用
- ⚡ **连接稳定**: 专业的数据库服务
- 🌍 **全球CDN**: 与Render兼容性好
- 🔧 **PostgreSQL**: 比MySQL更稳定
- 📊 **内置仪表板**: 方便管理

## 🚀 设置步骤

### 1. 注册Supabase
- 访问：https://supabase.com
- 使用GitHub账户登录

### 2. 创建项目
- 点击"New project"
- 项目名称：gemini-balance
- 数据库密码：设置一个强密码
- 区域：选择离你最近的

### 3. 获取连接信息
在项目设置中找到：
- Host
- Database name
- Port
- User
- Password

### 4. 修改gemini-balance配置
需要将MySQL配置改为PostgreSQL：

```env
DATABASE_TYPE=postgresql
POSTGRES_HOST=your-supabase-host
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-password
POSTGRES_DATABASE=postgres
```

## 💰 成本对比

| 服务 | 免费额度 | 稳定性 | 连接速度 |
|------|---------|--------|----------|
| Supabase | 500MB | 很高 | 很快 |
| Railway | $5额度 | 高 | 中等 |
| 本地MySQL | 免费 | 低 | 慢 |

## 🎯 推荐

对于生产环境，Supabase是最佳选择：
1. 完全免费
2. 连接稳定
3. 专业服务
4. 易于管理
