/**
 * gemini-balance JavaScript 使用示例
 * 支持 Node.js 和浏览器环境
 */

class GeminiBalanceClient {
    constructor(baseUrl, authToken) {
        this.baseUrl = baseUrl.replace(/\/$/, '');
        this.authToken = authToken;
        this.headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        };
    }

    /**
     * 聊天完成API调用
     */
    async chatCompletion(messages, options = {}) {
        const {
            model = 'gemini-1.5-flash',
            stream = false,
            temperature,
            maxTokens,
            ...otherOptions
        } = options;

        const data = {
            model,
            messages,
            stream,
            ...(temperature !== undefined && { temperature }),
            ...(maxTokens !== undefined && { max_tokens: maxTokens }),
            ...otherOptions
        };

        try {
            const response = await fetch(`${this.baseUrl}/v1/chat/completions`, {
                method: 'POST',
                headers: this.headers,
                body: JSON.stringify(data)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            if (stream) {
                return this.handleStreamResponse(response);
            } else {
                return await response.json();
            }
        } catch (error) {
            console.error('聊天请求失败:', error);
            throw error;
        }
    }

    /**
     * 处理流式响应
     */
    async handleStreamResponse(response) {
        const reader = response.body.getReader();
        const decoder = new TextDecoder();
        let content = '';

        try {
            while (true) {
                const { done, value } = await reader.read();
                if (done) break;

                const chunk = decoder.decode(value);
                const lines = chunk.split('\n');

                for (const line of lines) {
                    if (line.startsWith('data: ')) {
                        const data = line.slice(6);
                        if (data === '[DONE]') return { content };

                        try {
                            const parsed = JSON.parse(data);
                            const delta = parsed.choices?.[0]?.delta;
                            if (delta?.content) {
                                content += delta.content;
                                // 在浏览器中可以实时显示
                                if (typeof window !== 'undefined') {
                                    console.log(delta.content);
                                }
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    }
                }
            }
        } finally {
            reader.releaseLock();
        }

        return { content };
    }

    /**
     * 获取可用模型列表
     */
    async getModels() {
        try {
            const response = await fetch(`${this.baseUrl}/v1/models`, {
                headers: this.headers
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            return await response.json();
        } catch (error) {
            console.error('获取模型列表失败:', error);
            throw error;
        }
    }
}

// 使用示例
async function examples() {
    // 配置信息
    const BASE_URL = 'https://gemini-balance-9341.onrender.com';
    const AUTH_TOKEN = 'your-auth-token'; // 替换为你的实际AUTH_TOKEN

    const client = new GeminiBalanceClient(BASE_URL, AUTH_TOKEN);

    console.log('🚀 gemini-balance JavaScript 示例');
    console.log('='.repeat(50));

    try {
        // 示例1: 获取模型列表
        console.log('\n📋 示例1: 获取可用模型');
        const models = await client.getModels();
        console.log('可用模型:');
        models.data?.slice(0, 5).forEach(model => {
            console.log(`  - ${model.id}`);
        });

        // 示例2: 基本聊天
        console.log('\n💬 示例2: 基本聊天');
        const response1 = await client.chatCompletion([
            { role: 'user', content: '你好！请简单介绍一下自己。' }
        ]);
        console.log('AI回复:', response1.choices[0].message.content);

        // 示例3: 带参数的聊天
        console.log('\n⚙️ 示例3: 带参数的聊天');
        const response2 = await client.chatCompletion([
            { role: 'user', content: '请用创意的方式描述一只猫' }
        ], {
            temperature: 0.9,
            maxTokens: 200
        });
        console.log('AI回复:', response2.choices[0].message.content);

        // 示例4: 流式聊天
        console.log('\n🌊 示例4: 流式聊天');
        console.log('AI回复: ');
        const streamResponse = await client.chatCompletion([
            { role: 'user', content: '请写一首关于人工智能的短诗' }
        ], { stream: true });
        console.log('\n完整回复:', streamResponse.content);

    } catch (error) {
        console.error('示例执行失败:', error);
    }
}

// React Hook 示例
function useGeminiBalance(baseUrl, authToken) {
    const [client] = useState(() => new GeminiBalanceClient(baseUrl, authToken));
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const chat = useCallback(async (messages, options = {}) => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await client.chatCompletion(messages, options);
            return response;
        } catch (err) {
            setError(err.message);
            throw err;
        } finally {
            setLoading(false);
        }
    }, [client]);

    return { chat, loading, error };
}

// Vue Composition API 示例
function useGeminiBalanceVue(baseUrl, authToken) {
    const client = new GeminiBalanceClient(baseUrl, authToken);
    const loading = ref(false);
    const error = ref(null);

    const chat = async (messages, options = {}) => {
        loading.value = true;
        error.value = null;
        
        try {
            const response = await client.chatCompletion(messages, options);
            return response;
        } catch (err) {
            error.value = err.message;
            throw err;
        } finally {
            loading.value = false;
        }
    };

    return { chat, loading, error };
}

// Node.js 环境检测和执行
if (typeof window === 'undefined' && typeof module !== 'undefined') {
    // Node.js 环境
    const fetch = require('node-fetch'); // 需要安装: npm install node-fetch
    global.fetch = fetch;
    
    // 运行示例
    examples().catch(console.error);
}

// 导出供其他模块使用
if (typeof module !== 'undefined') {
    module.exports = { GeminiBalanceClient, useGeminiBalance, useGeminiBalanceVue };
}

// 浏览器环境全局变量
if (typeof window !== 'undefined') {
    window.GeminiBalanceClient = GeminiBalanceClient;
    window.useGeminiBalance = useGeminiBalance;
}
