# 🎯 Render + SQLite 配置 (最简单)
# 在Render平台部署，使用SQLite数据库（无需外部数据库）

# ==========================================
# 🗄️ 数据库配置
# ==========================================
DATABASE_TYPE=sqlite
SQLITE_DATABASE=gemini_balance.db

# ==========================================
# 🔑 API 配置 (请替换为实际值)
# ==========================================
API_KEYS=["your-gemini-api-key-here"]
AUTH_TOKEN=your-auth-token-here

# ==========================================
# ⚙️ 基础配置
# ==========================================
LOG_LEVEL=INFO
MAX_FAILURES=3
MAX_RETRIES=3
TIME_OUT=30

# ==========================================
# 🎨 功能配置
# ==========================================
STREAM_OPTIMIZER_ENABLED=false
URL_NORMALIZATION_ENABLED=false
TOOLS_CODE_EXECUTION_ENABLED=false
URL_CONTEXT_ENABLED=false
SHOW_SEARCH_LINK=true
SHOW_THINKING_PROCESS=true

# ==========================================
# 📡 流式输出配置
# ==========================================
FAKE_STREAM_ENABLED=false
STREAM_MIN_DELAY=0.01
STREAM_MAX_DELAY=0.05

# ==========================================
# ⏰ 调度器配置
# ==========================================
CHECK_INTERVAL_HOURS=1
TIMEZONE=Asia/Shanghai

# ==========================================
# 📝 日志管理
# ==========================================
AUTO_DELETE_ERROR_LOGS_ENABLED=true
AUTO_DELETE_ERROR_LOGS_DAYS=7
AUTO_DELETE_REQUEST_LOGS_ENABLED=false
AUTO_DELETE_REQUEST_LOGS_DAYS=30

# ==========================================
# 📁 文件管理
# ==========================================
FILES_CLEANUP_ENABLED=true
FILES_CLEANUP_INTERVAL_HOURS=1
FILES_USER_ISOLATION_ENABLED=true

# ==========================================
# 👨‍💼 管理员配置
# ==========================================
ADMIN_SESSION_EXPIRE=3600

# 🎯 SQLite 优势：
# ✅ 无需外部数据库
# ✅ 配置最简单
# ✅ 完全免费
# ✅ 无网络连接问题
# ✅ 适合个人使用

# ⚠️ SQLite 限制：
# - 不支持并发写入
# - 数据存储在容器中（重启可能丢失）
# - 不适合高并发场景

# 📝 部署步骤：
# 1. 在 Render 环境变量中添加这些配置
# 2. 替换 API_KEYS 和 AUTH_TOKEN
# 3. 部署完成，无需数据库设置
