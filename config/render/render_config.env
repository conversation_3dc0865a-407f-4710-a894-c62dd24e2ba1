# 🚀 Render 部署配置文件
# MySQL 容器网络信息已检查 ✅

# 📊 检查结果：
# - 容器内部 IP: **********
# - 端口映射: 3306/tcp -> 0.0.0.0:33060
# - MySQL 绑定: * (所有接口)
# - 本机内网 IP: ***********
# - 公网 IP: **************

# 🎯 方案一：使用公网 IP + 端口 33060（首选）
DATABASE_TYPE=mysql
MYSQL_HOST=**************
MYSQL_PORT=33060
MYSQL_USER=render_user
MYSQL_PASSWORD=Strong_Password_123!
MYSQL_DATABASE=gemini_balance

# 🔄 方案二：如果公网 IP 被阻止，尝试内网 IP
# DATABASE_TYPE=mysql
# MYSQL_HOST=***********
# MYSQL_PORT=33060
# MYSQL_USER=render_user
# MYSQL_PASSWORD=Strong_Password_123!
# MYSQL_DATABASE=gemini_balance

# 🚇 方案三：使用隧道服务（如果前两个方案都失败）
# 需要设置 ngrok 或 Cloudflare Tunnel

# 🔑 API 配置（请替换为实际值）
API_KEYS=["your-gemini-api-key-here"]
AUTH_TOKEN=your-auth-token-here

# ⚙️ 基础配置
LOG_LEVEL=INFO
MAX_FAILURES=3
MAX_RETRIES=3
TIME_OUT=30

# 📝 可选配置
STREAM_OPTIMIZER_ENABLED=false
URL_NORMALIZATION_ENABLED=false
TOOLS_CODE_EXECUTION_ENABLED=false

# 🔐 安全提醒：
# 1. MySQL 用户 render_user 已创建 ✅
# 2. 数据库 gemini_balance 已创建 ✅
# 3. 端口 33060 已映射到所有接口 ✅
# 4. 连接测试：本地成功，远程需要网络配置
