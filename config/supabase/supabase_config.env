# 🐘 Supabase PostgreSQL 配置 (免费稳定)
# 使用Supabase免费PostgreSQL数据库

# ==========================================
# 🗄️ 数据库配置
# ==========================================
DATABASE_TYPE=postgresql
POSTGRES_HOST=your-project-ref.supabase.co
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your-supabase-password
POSTGRES_DATABASE=postgres

# ==========================================
# 🔑 API 配置 (请替换为实际值)
# ==========================================
API_KEYS=["your-gemini-api-key-here"]
AUTH_TOKEN=your-auth-token-here

# ==========================================
# ⚙️ 基础配置
# ==========================================
LOG_LEVEL=INFO
MAX_FAILURES=3
MAX_RETRIES=3
TIME_OUT=30

# ==========================================
# 🎨 功能配置
# ==========================================
STREAM_OPTIMIZER_ENABLED=false
URL_NORMALIZATION_ENABLED=false
TOOLS_CODE_EXECUTION_ENABLED=false
URL_CONTEXT_ENABLED=false
SHOW_SEARCH_LINK=true
SHOW_THINKING_PROCESS=true

# ==========================================
# 📡 流式输出配置
# ==========================================
FAKE_STREAM_ENABLED=false
STREAM_MIN_DELAY=0.01
STREAM_MAX_DELAY=0.05

# ==========================================
# ⏰ 调度器配置
# ==========================================
CHECK_INTERVAL_HOURS=1
TIMEZONE=Asia/Shanghai

# ==========================================
# 📝 日志管理
# ==========================================
AUTO_DELETE_ERROR_LOGS_ENABLED=true
AUTO_DELETE_ERROR_LOGS_DAYS=7
AUTO_DELETE_REQUEST_LOGS_ENABLED=false
AUTO_DELETE_REQUEST_LOGS_DAYS=30

# ==========================================
# 📁 文件管理
# ==========================================
FILES_CLEANUP_ENABLED=true
FILES_CLEANUP_INTERVAL_HOURS=1
FILES_USER_ISOLATION_ENABLED=true

# ==========================================
# 👨‍💼 管理员配置
# ==========================================
ADMIN_SESSION_EXPIRE=3600

# 🎯 Supabase 优势：
# ✅ 完全免费 (500MB存储)
# ✅ PostgreSQL，功能强大
# ✅ 连接稳定，专业服务
# ✅ 全球CDN，速度快
# ✅ 内置管理界面

# 📝 设置步骤：
# 1. 注册 Supabase 账户: https://supabase.com
# 2. 创建新项目
# 3. 在项目设置中获取连接信息
# 4. 替换上面的连接参数
# 5. 需要修改 gemini-balance 支持 PostgreSQL

# ⚠️ 注意：
# 需要修改应用代码以支持PostgreSQL
# 或者使用MySQL兼容的PostgreSQL配置
