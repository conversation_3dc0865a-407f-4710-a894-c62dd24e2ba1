# 🌟 Render + PlanetScale 配置文件
# 将以下环境变量添加到 Render 的环境变量设置中

# 数据库配置 - PlanetScale
DATABASE_TYPE=mysql
MYSQL_HOST=aws.connect.psdb.cloud
MYSQL_PORT=3306
MYSQL_USER=<从PlanetScale复制用户名>
MYSQL_PASSWORD=<从PlanetScale复制密码>
MYSQL_DATABASE=gemini-balance

# SSL 配置（PlanetScale 需要）
MYSQL_SSL_DISABLED=false

# API 配置（请替换为你的实际值）
API_KEYS=["your-gemini-api-key-here"]
AUTH_TOKEN=your-auth-token-here

# 基础配置
LOG_LEVEL=INFO
MAX_FAILURES=3
MAX_RETRIES=3
TIME_OUT=30

# 可选配置
STREAM_OPTIMIZER_ENABLED=false
URL_NORMALIZATION_ENABLED=false
TOOLS_CODE_EXECUTION_ENABLED=false

# 🎯 设置说明：
# 1. 将 MYSQL_USER 和 MYSQL_PASSWORD 替换为 PlanetScale 提供的实际值
# 2. 添加你的 Gemini API Key 到 API_KEYS
# 3. 设置你的认证令牌到 AUTH_TOKEN
# 4. 其他配置可以保持默认值

# 📝 PlanetScale 特殊说明：
# - PlanetScale 强制使用 SSL 连接
# - 连接字符串格式：mysql://user:password@host:port/database?sslmode=require
# - 支持连接池和自动重连
