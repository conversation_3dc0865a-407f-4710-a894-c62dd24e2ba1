# 🚂 Render + Railway MySQL 优化配置
# 针对云平台间连接优化的环境变量

# 数据库配置 - Railway MySQL
DATABASE_TYPE=mysql
MYSQL_HOST=yamanote.proxy.rlwy.net
MYSQL_PORT=27669
MYSQL_USER=root
MYSQL_PASSWORD=gplFWFaZqFYSkhXZdUtzmNofkKyIFIam
MYSQL_DATABASE=railway

# SSL 配置（Railway 不需要 SSL）
MYSQL_SSL_DISABLED=true

# API 配置（请替换为你的实际值）
API_KEYS=["your-gemini-api-key-here"]
AUTH_TOKEN=your-auth-token-here

# 基础配置
LOG_LEVEL=INFO
MAX_FAILURES=3
MAX_RETRIES=3
TIME_OUT=60

# 数据库连接优化（针对云平台间连接）
# 这些参数会在代码中自动应用到Railway连接

# 可选配置
STREAM_OPTIMIZER_ENABLED=false
URL_NORMALIZATION_ENABLED=false
TOOLS_CODE_EXECUTION_ENABLED=false

# 🔧 优化说明：
# 1. 增加了连接超时时间（30秒）
# 2. 减少了连接池大小以避免过多连接
# 3. 添加了重试机制
# 4. 优化了连接回收时间

# 📝 部署步骤：
# 1. 将这些环境变量复制到 Render 的 Environment 设置中
# 2. 替换 API_KEYS 和 AUTH_TOKEN 为你的实际值
# 3. 保存并重新部署

# 🚨 注意事项：
# - Railway 连接可能需要几秒钟建立
# - 如果仍有连接问题，可以考虑使用 Railway 部署应用
# - 或者使用其他云数据库服务
