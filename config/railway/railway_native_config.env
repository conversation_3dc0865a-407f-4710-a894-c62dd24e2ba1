# 🚂 Railway 原生部署配置 (推荐)
# 在Railway平台部署gemini-balance，使用Railway MySQL内网连接

# ==========================================
# 🗄️ 数据库配置
# ==========================================
DATABASE_TYPE=mysql
MYSQL_HOST=mysql.railway.internal
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=gplFWFaZqFYSkhXZdUtzmNofkKyIFIam
MYSQL_DATABASE=railway
MYSQL_SSL_DISABLED=true

# ==========================================
# 🔑 API 配置 (请替换为实际值)
# ==========================================
API_KEYS=["your-gemini-api-key-here"]
AUTH_TOKEN=your-auth-token-here

# ==========================================
# ⚙️ 基础配置
# ==========================================
LOG_LEVEL=INFO
MAX_FAILURES=3
MAX_RETRIES=3
TIME_OUT=30

# ==========================================
# 🎨 功能配置
# ==========================================
STREAM_OPTIMIZER_ENABLED=false
URL_NORMALIZATION_ENABLED=false
TOOLS_CODE_EXECUTION_ENABLED=false
URL_CONTEXT_ENABLED=false
SHOW_SEARCH_LINK=true
SHOW_THINKING_PROCESS=true

# ==========================================
# 📡 流式输出配置
# ==========================================
FAKE_STREAM_ENABLED=false
STREAM_MIN_DELAY=0.01
STREAM_MAX_DELAY=0.05

# ==========================================
# ⏰ 调度器配置
# ==========================================
CHECK_INTERVAL_HOURS=1
TIMEZONE=Asia/Shanghai

# ==========================================
# 📝 日志管理
# ==========================================
AUTO_DELETE_ERROR_LOGS_ENABLED=true
AUTO_DELETE_ERROR_LOGS_DAYS=7
AUTO_DELETE_REQUEST_LOGS_ENABLED=false
AUTO_DELETE_REQUEST_LOGS_DAYS=30

# ==========================================
# 📁 文件管理
# ==========================================
FILES_CLEANUP_ENABLED=true
FILES_CLEANUP_INTERVAL_HOURS=1
FILES_USER_ISOLATION_ENABLED=true

# ==========================================
# 👨‍💼 管理员配置
# ==========================================
ADMIN_SESSION_EXPIRE=3600

# 🎯 Railway部署优势：
# ✅ 内网连接，速度快 (mysql.railway.internal)
# ✅ 同平台部署，稳定性高
# ✅ 免费额度充足 ($5/月)
# ✅ 配置简单，一键部署
# ✅ 无网络延迟问题

# 📝 部署步骤：
# 1. 登录 Railway 控制台
# 2. 新建项目，连接 GitHub 仓库
# 3. 添加这些环境变量
# 4. 替换 API_KEYS 和 AUTH_TOKEN
# 5. 部署完成
