#!/usr/bin/env python3
"""
使用 Gemini 原生 API 格式调用 gemini-balance 服务
API 路径: /gemini/v1beta
"""

import requests
import json
import time
import urllib3
from typing import Dict, List, Any, Optional

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class GeminiNativeClient:
    def __init__(self, base_url: str, auth_token: str):
        """
        初始化 Gemini 原生 API 客户端
        
        Args:
            base_url: gemini-balance服务地址
            auth_token: 验证令牌
        """
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建配置好的requests session"""
        session = requests.Session()
        session.verify = False
        session.headers.update({
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.auth_token}',
            'x-goog-api-key': self.auth_token,  # Gemini 原生 API 需要的头部
            'User-Agent': 'GeminiNativeClient/1.0'
        })
        return session
    
    def get_models(self) -> Dict[str, Any]:
        """
        获取可用模型列表
        GET /gemini/v1beta/models
        """
        url = f"{self.base_url}/gemini/v1beta/models"
        
        try:
            print(f"🔍 获取模型列表: {url}")
            response = self.session.get(url, timeout=(10, 30))
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            return {"error": f"请求失败: {e}"}
    
    def generate_content(self, model_name: str, contents: List[Dict], 
                        generation_config: Optional[Dict] = None) -> Dict[str, Any]:
        """
        生成内容 (非流式)
        POST /gemini/v1beta/models/{model_name}:generateContent
        
        Args:
            model_name: 模型名称，如 "gemini-1.5-flash"
            contents: 内容列表，格式为 [{"parts": [{"text": "..."}]}]
            generation_config: 生成配置
        """
        url = f"{self.base_url}/gemini/v1beta/models/{model_name}:generateContent"
        
        data = {
            "contents": contents
        }
        
        if generation_config:
            data["generationConfig"] = generation_config
        
        try:
            print(f"🤖 调用模型: {model_name}")
            response = self.session.post(url, json=data, timeout=(15, 120))
            
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            return {"error": f"请求失败: {e}"}
    
    def stream_generate_content(self, model_name: str, contents: List[Dict],
                               generation_config: Optional[Dict] = None) -> Dict[str, Any]:
        """
        流式生成内容
        POST /gemini/v1beta/models/{model_name}:streamGenerateContent
        
        Args:
            model_name: 模型名称
            contents: 内容列表
            generation_config: 生成配置
        """
        url = f"{self.base_url}/gemini/v1beta/models/{model_name}:streamGenerateContent"
        
        data = {
            "contents": contents
        }
        
        if generation_config:
            data["generationConfig"] = generation_config
        
        try:
            print(f"🌊 流式调用模型: {model_name}")
            response = self.session.post(url, json=data, timeout=(15, 120), stream=True)
            
            if response.status_code == 200:
                return self._handle_stream_response(response)
            else:
                return {"error": f"HTTP {response.status_code}: {response.text}"}
                
        except Exception as e:
            return {"error": f"请求失败: {e}"}
    
    def _handle_stream_response(self, response):
        """处理流式响应"""
        full_content = ""
        
        try:
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data = line[6:]
                        if data == '[DONE]':
                            break
                        try:
                            chunk = json.loads(data)
                            # Gemini 流式响应格式
                            if 'candidates' in chunk:
                                for candidate in chunk['candidates']:
                                    if 'content' in candidate and 'parts' in candidate['content']:
                                        for part in candidate['content']['parts']:
                                            if 'text' in part:
                                                text = part['text']
                                                full_content += text
                                                print(text, end='', flush=True)
                        except json.JSONDecodeError:
                            continue
            
            return {"content": full_content}
            
        except Exception as e:
            return {"error": f"流式响应处理失败: {e}"}

def main():
    """主函数 - 演示 Gemini 原生 API 调用"""
    
    # 配置信息
    BASE_URL = "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    
    print("🚀 Gemini 原生 API 格式调用测试")
    print("=" * 50)
    print(f"服务地址: {BASE_URL}")
    print(f"验证令牌: {AUTH_TOKEN}")
    print(f"API 路径: /gemini/v1beta")
    print()
    
    # 创建客户端
    client = GeminiNativeClient(BASE_URL, AUTH_TOKEN)
    
    # 1. 获取模型列表
    print("📋 1. 获取可用模型列表")
    print("-" * 30)
    models = client.get_models()
    
    if "error" not in models:
        print("✅ 获取成功!")
        if "models" in models:
            print("可用模型:")
            for model in models["models"][:10]:  # 显示前10个
                model_name = model.get("name", "unknown")
                display_name = model.get("displayName", "")
                print(f"  - {model_name} ({display_name})")
            
            if len(models["models"]) > 10:
                print(f"  ... 还有 {len(models['models']) - 10} 个模型")
        else:
            print("模型列表格式:", json.dumps(models, indent=2, ensure_ascii=False)[:200])
    else:
        print(f"❌ 获取失败: {models['error']}")
    
    print()
    
    # 2. 测试内容生成 (非流式)
    print("💬 2. 测试内容生成 (非流式)")
    print("-" * 30)
    
    contents = [
        {
            "parts": [
                {"text": "你好！请简单介绍一下你自己，并说明你的版本和能力。"}
            ]
        }
    ]
    
    generation_config = {
        "temperature": 0.7,
        "maxOutputTokens": 200
    }
    
    response = client.generate_content(
        model_name="gemini-1.5-flash",
        contents=contents,
        generation_config=generation_config
    )
    
    if "error" not in response:
        print("✅ 生成成功!")
        if "candidates" in response:
            for candidate in response["candidates"]:
                if "content" in candidate and "parts" in candidate["content"]:
                    for part in candidate["content"]["parts"]:
                        if "text" in part:
                            print(f"AI回复: {part['text']}")
        else:
            print("响应格式:", json.dumps(response, indent=2, ensure_ascii=False)[:300])
    else:
        print(f"❌ 生成失败: {response['error']}")
    
    print()
    
    # 3. 测试流式内容生成
    print("🌊 3. 测试流式内容生成")
    print("-" * 30)
    
    stream_contents = [
        {
            "parts": [
                {"text": "请写一首关于人工智能的短诗，要有创意和想象力。"}
            ]
        }
    ]
    
    print("AI回复: ", end="")
    stream_response = client.stream_generate_content(
        model_name="gemini-1.5-flash",
        contents=stream_contents,
        generation_config={"temperature": 0.9, "maxOutputTokens": 300}
    )
    
    print()  # 换行
    
    if "error" in stream_response:
        print(f"❌ 流式生成失败: {stream_response['error']}")
    else:
        print("✅ 流式生成完成!")
    
    print()

def test_different_models():
    """测试不同的模型"""
    BASE_URL = "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    
    client = GeminiNativeClient(BASE_URL, AUTH_TOKEN)
    
    print("🔄 4. 测试不同模型")
    print("-" * 30)
    
    models_to_test = [
        "gemini-1.5-flash",
        "gemini-1.5-pro",
        "gemini-2.0-flash-exp",
        "gemini-2.5-flash"
    ]
    
    contents = [
        {
            "parts": [
                {"text": "请用一句话描述什么是人工智能。"}
            ]
        }
    ]
    
    for model in models_to_test:
        print(f"\n🤖 测试模型: {model}")
        
        response = client.generate_content(
            model_name=model,
            contents=contents,
            generation_config={"maxOutputTokens": 100}
        )
        
        if "error" not in response and "candidates" in response:
            for candidate in response["candidates"]:
                if "content" in candidate and "parts" in candidate["content"]:
                    for part in candidate["content"]["parts"]:
                        if "text" in part:
                            print(f"✅ {model}: {part['text'][:80]}...")
                            break
        else:
            error_msg = response.get("error", "未知错误")
            print(f"❌ {model}: {error_msg}")

def show_curl_examples():
    """显示 curl 命令示例"""
    print("\n🔧 等效的 curl 命令示例")
    print("=" * 40)
    
    base_url = "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com"
    token = "12345"
    
    examples = f'''
# 1. 获取模型列表
curl -H "Authorization: Bearer {token}" \\
  -k --connect-timeout 10 --max-time 30 \\
  {base_url}/gemini/v1beta/models

# 2. 生成内容 (非流式)
curl -X POST {base_url}/gemini/v1beta/models/gemini-1.5-flash:generateContent \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {token}" \\
  -k --connect-timeout 15 --max-time 120 \\
  -d '{{
    "contents": [
      {{
        "parts": [
          {{"text": "你好，世界！"}}
        ]
      }}
    ],
    "generationConfig": {{
      "temperature": 0.7,
      "maxOutputTokens": 200
    }}
  }}'

# 3. 流式生成内容
curl -X POST {base_url}/gemini/v1beta/models/gemini-1.5-flash:streamGenerateContent \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {token}" \\
  -k --connect-timeout 15 --max-time 120 \\
  -d '{{
    "contents": [
      {{
        "parts": [
          {{"text": "请写一个故事"}}
        ]
      }}
    ]
  }}'
'''
    print(examples)

if __name__ == "__main__":
    main()
    test_different_models()
    show_curl_examples()
    
    print("\n" + "=" * 50)
    print("📝 Gemini 原生 API 格式总结:")
    print("✅ 使用 /gemini/v1beta 路径")
    print("✅ 支持获取模型列表")
    print("✅ 支持非流式内容生成")
    print("✅ 支持流式内容生成")
    print("✅ 使用 Gemini 原生的请求/响应格式")
    print("\n🎯 推荐用法:")
    print("- 简单对话: 使用 generateContent")
    print("- 长文本生成: 使用 streamGenerateContent")
    print("- 调整 temperature 控制创造性")
    print("- 设置 maxOutputTokens 限制输出长度")
