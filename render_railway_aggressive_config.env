# 🚂 Render + Railway 激进优化配置
# 针对连接超时问题的激进优化方案

# 数据库配置 - Railway MySQL
DATABASE_TYPE=mysql
MYSQL_HOST=yamanote.proxy.rlwy.net
MYSQL_PORT=27669
MYSQL_USER=root
MYSQL_PASSWORD=gplFWFaZqFYSkhXZdUtzmNofkKyIFIam
MYSQL_DATABASE=railway

# SSL 配置
MYSQL_SSL_DISABLED=true

# 激进的超时配置
TIME_OUT=120
MAX_RETRIES=5
MAX_FAILURES=5

# API 配置（请替换为实际值）
API_KEYS=["your-gemini-api-key-here"]
AUTH_TOKEN=your-auth-token-here

# 基础配置
LOG_LEVEL=DEBUG
STREAM_OPTIMIZER_ENABLED=false
URL_NORMALIZATION_ENABLED=false
TOOLS_CODE_EXECUTION_ENABLED=false

# 🔧 激进优化说明：
# 1. 超时时间增加到120秒
# 2. 重试次数增加到5次
# 3. 日志级别设为DEBUG以便诊断
# 4. 数据库连接池参数在代码中已优化

# 📝 使用说明：
# 1. 在Render环境变量中使用这些配置
# 2. 如果仍然超时，建议切换到Railway部署
# 3. 或考虑使用其他数据库服务
