# 🚀 快速修复：切换到SQLite

## 立即解决数据库连接问题

### 📋 在Render中修改环境变量

将以下环境变量添加到Render：

```env
# 数据库配置 - 切换到SQLite
DATABASE_TYPE=sqlite
SQLITE_DATABASE=gemini_balance.db

# 删除或注释掉所有MySQL相关配置
# MYSQL_HOST=
# MYSQL_PORT=
# MYSQL_USER=
# MYSQL_PASSWORD=
# MYSQL_DATABASE=
# MYSQL_SSL_DISABLED=

# API配置（保持不变）
API_KEYS=["your-gemini-api-key"]
AUTH_TOKEN=your-auth-token

# 基础配置
LOG_LEVEL=INFO
MAX_FAILURES=3
MAX_RETRIES=3
TIME_OUT=30
```

### ✅ SQLite优势

- ✅ **立即生效** - 无需外部数据库
- ✅ **完全免费** - 无额外费用
- ✅ **无网络问题** - 本地文件数据库
- ✅ **配置简单** - 只需修改一个变量

### ⚠️ SQLite限制

- 数据存储在容器中（重启时数据可能丢失）
- 不支持高并发写入
- 适合个人使用或小型项目

### 🚀 操作步骤

1. 登录Render控制台
2. 进入你的服务设置
3. 点击"Environment"
4. 修改 `DATABASE_TYPE=sqlite`
5. 删除所有MYSQL_*变量
6. 保存并重新部署

**5分钟内解决问题！**
