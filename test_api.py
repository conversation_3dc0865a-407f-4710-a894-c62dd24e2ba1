import requests
import json

# 配置信息
BASE_URL = "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com"
AUTH_TOKEN = "12345"
HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Bearer {AUTH_TOKEN}'
}

# 1. 获取可用模型列表
print("正在获取可用模型列表...")
try:
    response = requests.get(f"{BASE_URL}/v1/models", headers=HEADERS, timeout=20)
    response.raise_for_status()  # 如果状态码不是 2xx，则引发异常
    models = response.json()
    print("✅ 可用模型:")
    if models and "data" in models:
        for model in models["data"]:
            print(f"  - {model.get('id', 'N/A')}")
    else:
        print("  - 未找到模型数据或响应为空")
    print("\n原始响应:")
    print(json.dumps(models, indent=2, ensure_ascii=False))
except requests.exceptions.RequestException as e:
    print(f"❌ 获取模型列表失败: {e}")


# 2. 基本聊天调用
print("\n正在进行基本聊天调用...")
try:
    data = {
        "model": "gemini-1.5-flash",
        "messages": [{"role": "user", "content": "你好！请用中文简单介绍一下你自己。"}],
        "stream": False
    }
    response = requests.post(f"{BASE_URL}/v1/chat/completions", headers=HEADERS, json=data, timeout=60)
    response.raise_for_status()
    chat_response = response.json()
    print("✅ AI 回复:")
    if "choices" in chat_response and chat_response["choices"]:
        print(chat_response["choices"][0]["message"]["content"])
    else:
        print("  - 未收到有效的回复")
    print("\n原始响应:")
    print(json.dumps(chat_response, indent=2, ensure_ascii=False))
except requests.exceptions.RequestException as e:
    print(f"❌ 聊天调用失败: {e}")


# 3. 流式聊天调用
print("\n正在进行流式聊天调用...")
try:
    data = {
        "model": "gemini-1.5-flash",
        "messages": [{"role": "user", "content": "请写一首关于宇宙的五行短诗"}],
        "stream": True
    }
    with requests.post(f"{BASE_URL}/v1/chat/completions", headers=HEADERS, json=data, stream=True, timeout=60) as response:
        response.raise_for_status()
        print("✅ 流式 AI 回复:")
        for line in response.iter_lines():
            if line:
                line_str = line.decode('utf-8')
                if line_str.startswith('data: '):
                    json_str = line_str[6:]
                    if json_str == '[DONE]':
                        break
                    try:
                        chunk = json.loads(json_str)
                        if chunk.get("choices") and chunk["choices"][0].get("delta", {}).get("content"):
                            print(chunk["choices"][0]["delta"]["content"], end="")
                    except json.JSONDecodeError:
                        print(f"\n无法解析的JSON: {json_str}")
        print()
except requests.exceptions.RequestException as e:
    print(f"❌ 流式聊天调用失败: {e}")

print("\n🎉 测试完成！")