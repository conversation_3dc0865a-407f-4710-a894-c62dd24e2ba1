#!/usr/bin/env python3
"""
稳定的 Gemini Balance 客户端
解决SSL连接问题，提供多种调用方式
"""

import requests
import json
import time
import urllib3
from typing import Dict, List, Any, Optional

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

class StableGeminiClient:
    def __init__(self, base_url: str, auth_token: str):
        """
        初始化稳定的Gemini客户端
        
        Args:
            base_url: gemini-balance服务地址
            auth_token: 认证令牌
        """
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.session = self._create_session()
    
    def _create_session(self) -> requests.Session:
        """创建配置好的requests session"""
        session = requests.Session()
        
        # SSL配置
        session.verify = False
        
        # 连接池配置
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=20,
            max_retries=3,
            pool_block=False
        )
        session.mount('http://', adapter)
        session.mount('https://', adapter)
        
        # 默认headers
        session.headers.update({
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {self.auth_token}',
            'User-Agent': 'StableGeminiClient/1.0'
        })
        
        return session
    
    def chat_completion(self, messages: List[Dict], model: str = "gemini-1.5-flash", 
                       stream: bool = False, max_retries: int = 3, **kwargs) -> Dict[str, Any]:
        """
        聊天完成API调用 - 带重试机制
        
        Args:
            messages: 消息列表
            model: 模型名称
            stream: 是否流式响应
            max_retries: 最大重试次数
            **kwargs: 其他参数
        """
        url = f"{self.base_url}/v1/chat/completions"
        
        data = {
            "model": model,
            "messages": messages,
            "stream": stream,
            **kwargs
        }
        
        last_error = None
        
        for attempt in range(max_retries):
            try:
                print(f"🔄 尝试调用 {model} (第 {attempt + 1}/{max_retries} 次)")
                
                response = self.session.post(
                    url, 
                    json=data, 
                    timeout=(15, 120),  # (连接超时, 读取超时)
                    stream=stream
                )
                
                # 检查HTTP状态码
                if response.status_code == 200:
                    if stream:
                        return self._handle_stream_response(response)
                    else:
                        return response.json()
                else:
                    print(f"❌ HTTP错误 {response.status_code}: {response.text}")
                    last_error = f"HTTP {response.status_code}: {response.text}"
                    
            except (requests.exceptions.SSLError, 
                   requests.exceptions.ConnectionError,
                   requests.exceptions.Timeout) as e:
                print(f"⚠️  连接错误 (尝试 {attempt + 1}/{max_retries}): {e}")
                last_error = str(e)
                
                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 2  # 递增等待时间
                    print(f"⏳ 等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                    
            except Exception as e:
                print(f"❌ 未知错误: {e}")
                last_error = str(e)
                break
        
        return {"error": f"所有重试失败，最后错误: {last_error}"}
    
    def _handle_stream_response(self, response):
        """处理流式响应"""
        content = ""
        try:
            for line in response.iter_lines():
                if line:
                    line = line.decode('utf-8')
                    if line.startswith('data: '):
                        data = line[6:]
                        if data == '[DONE]':
                            break
                        try:
                            chunk = json.loads(data)
                            if 'choices' in chunk and chunk['choices']:
                                delta = chunk['choices'][0].get('delta', {})
                                if 'content' in delta:
                                    content += delta['content']
                                    print(delta['content'], end='', flush=True)
                        except json.JSONDecodeError:
                            continue
        except Exception as e:
            return {"error": f"流式响应处理失败: {e}"}
        
        return {"content": content}
    
    def get_models(self) -> Dict[str, Any]:
        """获取可用模型列表"""
        url = f"{self.base_url}/v1/models"
        
        try:
            response = self.session.get(url, timeout=(10, 30))
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"error": f"获取模型列表失败: {e}"}
    
    def test_connection(self) -> bool:
        """测试连接是否正常"""
        try:
            models = self.get_models()
            return "error" not in models
        except:
            return False

def main():
    """主函数 - 演示稳定调用"""
    
    # 配置信息 - 请根据你的实际情况修改
    BASE_URL = "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    
    print("🚀 稳定的 Gemini Balance 客户端测试")
    print("=" * 50)
    print(f"服务地址: {BASE_URL}")
    print(f"认证令牌: {AUTH_TOKEN}")
    print()
    
    # 创建客户端
    client = StableGeminiClient(BASE_URL, AUTH_TOKEN)
    
    # 测试连接
    print("🔍 测试连接...")
    if client.test_connection():
        print("✅ 连接测试成功")
    else:
        print("❌ 连接测试失败，但继续尝试...")
    print()
    
    # 测试可用模型
    available_models = [
        "gemini-1.5-flash",
        "gemini-1.5-pro", 
        "gemini-2.0-flash-exp",
        "gemini-2.5-flash",
        "gemini-2.5-pro"
    ]
    
    for model in available_models:
        print(f"🤖 测试模型: {model}")
        
        messages = [
            {"role": "user", "content": f"你好！请简单介绍一下你自己，并确认你是 {model} 模型。"}
        ]
        
        response = client.chat_completion(
            messages=messages,
            model=model,
            max_tokens=200,
            temperature=0.7
        )
        
        if "error" not in response and "choices" in response:
            content = response["choices"][0]["message"]["content"]
            print(f"✅ 成功: {content[:100]}...")
        else:
            error_msg = response.get("error", "未知错误")
            print(f"❌ 失败: {error_msg}")
        
        print("-" * 30)
        time.sleep(1)  # 避免请求过快

def test_specific_model():
    """测试特定模型"""
    BASE_URL = "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com"
    AUTH_TOKEN = "12345"
    
    client = StableGeminiClient(BASE_URL, AUTH_TOKEN)
    
    print("\n💻 测试代码生成能力")
    print("=" * 30)
    
    messages = [
        {"role": "user", "content": "请写一个Python函数，计算斐波那契数列的第n项，要求使用递归方法"}
    ]
    
    response = client.chat_completion(
        messages=messages,
        model="gemini-1.5-flash",  # 使用较稳定的模型
        max_tokens=500,
        temperature=0.3
    )
    
    if "error" not in response and "choices" in response:
        content = response["choices"][0]["message"]["content"]
        print("✅ 代码生成成功:")
        print(content)
    else:
        error_msg = response.get("error", "未知错误")
        print(f"❌ 代码生成失败: {error_msg}")

if __name__ == "__main__":
    main()
    test_specific_model()
    
    print("\n" + "=" * 50)
    print("📝 使用建议:")
    print("1. 如果仍有SSL错误，尝试使用不同的网络环境")
    print("2. 检查服务器地址是否正确")
    print("3. 确认认证令牌是否有效")
    print("4. 可以尝试使用代理或VPN")
    print("5. 联系服务提供商确认服务状态")
