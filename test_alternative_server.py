#!/usr/bin/env python3
"""
测试备用服务器地址
"""

import requests
import json
import time
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_server(base_url, auth_token, server_name):
    """测试指定服务器"""
    print(f"\n🔍 测试服务器: {server_name}")
    print(f"地址: {base_url}")
    print("-" * 50)
    
    # 创建session
    session = requests.Session()
    session.verify = False
    session.headers.update({
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {auth_token}',
        'User-Agent': 'TestClient/1.0'
    })
    
    # 测试连接
    try:
        print("📡 测试基本连接...")
        response = session.get(f"{base_url}/v1/models", timeout=(10, 30))
        if response.status_code == 200:
            print("✅ 连接成功")
            models_data = response.json()
            if "data" in models_data:
                print(f"📋 可用模型数量: {len(models_data['data'])}")
                for model in models_data["data"][:5]:  # 显示前5个模型
                    print(f"  - {model.get('id', 'unknown')}")
                if len(models_data["data"]) > 5:
                    print(f"  ... 还有 {len(models_data['data']) - 5} 个模型")
        else:
            print(f"❌ 连接失败: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return False
    
    # 测试聊天API
    print("\n💬 测试聊天API...")
    try:
        chat_data = {
            "model": "gemini-1.5-flash",
            "messages": [
                {"role": "user", "content": "你好！请简单回复确认你能正常工作。"}
            ],
            "max_tokens": 100,
            "temperature": 0.7
        }
        
        response = session.post(
            f"{base_url}/v1/chat/completions",
            json=chat_data,
            timeout=(15, 60)
        )
        
        if response.status_code == 200:
            result = response.json()
            if "choices" in result and result["choices"]:
                content = result["choices"][0]["message"]["content"]
                print(f"✅ 聊天成功: {content[:80]}...")
                return True
            else:
                print(f"❌ 聊天失败: 响应格式错误")
                return False
        else:
            print(f"❌ 聊天失败: HTTP {response.status_code}")
            print(f"错误信息: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ 聊天失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Gemini Balance 服务器连接测试")
    print("=" * 60)
    
    # 要测试的服务器列表
    servers = [
        {
            "name": "服务器1 (ap-southeast-1)",
            "url": "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com",
            "token": "12345"
        },
        {
            "name": "服务器2 (ap-northeast-1)", 
            "url": "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com",
            "token": "12345"
        },
        {
            "name": "Render服务器",
            "url": "https://gemini-balance-9341.onrender.com",
            "token": "12345"
        }
    ]
    
    working_servers = []
    
    for server in servers:
        success = test_server(server["url"], server["token"], server["name"])
        if success:
            working_servers.append(server)
        time.sleep(2)  # 避免请求过快
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    if working_servers:
        print(f"✅ 找到 {len(working_servers)} 个可用服务器:")
        for server in working_servers:
            print(f"  - {server['name']}: {server['url']}")
        
        print(f"\n🎯 推荐使用: {working_servers[0]['name']}")
        print(f"地址: {working_servers[0]['url']}")
        print(f"令牌: {working_servers[0]['token']}")
        
        # 生成配置示例
        print(f"\n📋 配置示例:")
        print(f"BASE_URL = \"{working_servers[0]['url']}\"")
        print(f"AUTH_TOKEN = \"{working_servers[0]['token']}\"")
        
    else:
        print("❌ 没有找到可用的服务器")
        print("\n🔧 可能的解决方案:")
        print("1. 检查网络连接")
        print("2. 尝试使用VPN或代理")
        print("3. 确认认证令牌是否正确")
        print("4. 联系服务提供商")

def test_with_curl():
    """提供curl测试命令"""
    print(f"\n🔧 手动测试命令 (curl):")
    print("-" * 30)
    
    servers = [
        "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com",
        "https://vnupxefbkhtx.ap-northeast-1.clawcloudrun.com", 
        "https://gemini-balance-9341.onrender.com"
    ]
    
    for i, server in enumerate(servers, 1):
        print(f"\n# 测试服务器 {i}")
        print(f"curl -k -H \"Authorization: Bearer 12345\" \\")
        print(f"  --connect-timeout 10 --max-time 30 \\")
        print(f"  {server}/v1/models")

if __name__ == "__main__":
    main()
    test_with_curl()
