#!/usr/bin/env python3
"""
使用 OpenAI SDK 调用 Gemini Balance 服务
这种方式通常更稳定，因为SDK处理了很多底层细节
"""

try:
    import openai
    SDK_AVAILABLE = True
except ImportError:
    SDK_AVAILABLE = False
    print("❌ OpenAI SDK 未安装")
    print("请运行: pip install openai")

import json
import time
from typing import List, Dict

def test_with_openai_sdk():
    """使用OpenAI SDK测试"""
    if not SDK_AVAILABLE:
        return
    
    # 配置信息
    BASE_URL = "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com/v1"
    AUTH_TOKEN = "12345"
    
    print("🔌 使用 OpenAI SDK 调用 Gemini Balance")
    print("=" * 50)
    print(f"服务地址: {BASE_URL}")
    print(f"认证令牌: {AUTH_TOKEN}")
    print()
    
    # 创建客户端
    client = openai.OpenAI(
        api_key=AUTH_TOKEN,
        base_url=BASE_URL,
        timeout=60.0,
        max_retries=3
    )
    
    # 测试模型列表
    print("📋 获取模型列表...")
    try:
        models = client.models.list()
        print("✅ 可用模型:")
        for model in models.data:
            print(f"  - {model.id}")
        print()
    except Exception as e:
        print(f"❌ 获取模型列表失败: {e}")
        print()
    
    # 测试聊天完成
    test_models = [
        "gemini-1.5-flash",
        "gemini-1.5-pro",
        "gemini-2.0-flash-exp"
    ]
    
    for model in test_models:
        print(f"🤖 测试模型: {model}")
        
        try:
            response = client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": f"你好！请确认你是 {model} 模型，并简单介绍你的能力。"}
                ],
                max_tokens=200,
                temperature=0.7
            )
            
            content = response.choices[0].message.content
            print(f"✅ 成功: {content[:100]}...")
            
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        print("-" * 30)
        time.sleep(1)
    
    # 测试流式响应
    print("\n🌊 测试流式响应")
    try:
        stream = client.chat.completions.create(
            model="gemini-1.5-flash",
            messages=[
                {"role": "user", "content": "请写一首关于人工智能的短诗"}
            ],
            stream=True,
            max_tokens=300
        )
        
        print("AI回复: ", end="")
        for chunk in stream:
            if chunk.choices[0].delta.content is not None:
                print(chunk.choices[0].delta.content, end="", flush=True)
        print()  # 换行
        
    except Exception as e:
        print(f"❌ 流式响应失败: {e}")

def test_code_generation():
    """测试代码生成"""
    if not SDK_AVAILABLE:
        return
    
    BASE_URL = "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com/v1"
    AUTH_TOKEN = "12345"
    
    client = openai.OpenAI(
        api_key=AUTH_TOKEN,
        base_url=BASE_URL,
        timeout=60.0
    )
    
    print("\n💻 测试代码生成能力")
    print("=" * 30)
    
    try:
        response = client.chat.completions.create(
            model="gemini-1.5-flash",
            messages=[
                {
                    "role": "user", 
                    "content": """请写一个Python类，实现一个简单的计算器，包含以下功能：
1. 加法
2. 减法  
3. 乘法
4. 除法
5. 历史记录功能

请包含完整的代码和使用示例。"""
                }
            ],
            max_tokens=1000,
            temperature=0.3
        )
        
        content = response.choices[0].message.content
        print("✅ 代码生成成功:")
        print(content)
        
    except Exception as e:
        print(f"❌ 代码生成失败: {e}")

def test_conversation():
    """测试多轮对话"""
    if not SDK_AVAILABLE:
        return
    
    BASE_URL = "https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com/v1"
    AUTH_TOKEN = "12345"
    
    client = openai.OpenAI(
        api_key=AUTH_TOKEN,
        base_url=BASE_URL
    )
    
    print("\n🔄 测试多轮对话")
    print("=" * 30)
    
    # 模拟对话历史
    conversation = [
        {"role": "user", "content": "我想学习机器学习"},
        {"role": "assistant", "content": "太好了！机器学习是一个非常有趣的领域。你有编程基础吗？"},
        {"role": "user", "content": "我会Python，但对机器学习是新手"},
        {"role": "assistant", "content": "很好！Python是机器学习的主要语言。我建议你从以下几个方面开始学习..."},
        {"role": "user", "content": "请推荐一些具体的学习资源和项目"}
    ]
    
    try:
        response = client.chat.completions.create(
            model="gemini-1.5-flash",
            messages=conversation,
            max_tokens=500,
            temperature=0.7
        )
        
        content = response.choices[0].message.content
        print("AI回复:")
        print(content)
        
    except Exception as e:
        print(f"❌ 多轮对话失败: {e}")

def show_curl_examples():
    """显示curl命令示例"""
    print("\n🔧 等效的 curl 命令示例")
    print("=" * 40)
    
    curl_examples = '''
# 基本聊天请求
curl -X POST https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer 12345" \\
  -k \\
  --connect-timeout 15 \\
  --max-time 120 \\
  -d '{
    "model": "gemini-1.5-flash",
    "messages": [
      {"role": "user", "content": "你好，世界！"}
    ],
    "max_tokens": 200,
    "temperature": 0.7
  }'

# 流式请求
curl -X POST https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com/v1/chat/completions \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer 12345" \\
  -k \\
  --connect-timeout 15 \\
  --max-time 120 \\
  -d '{
    "model": "gemini-1.5-flash",
    "messages": [
      {"role": "user", "content": "请写一个故事"}
    ],
    "stream": true
  }'

# 获取模型列表
curl -H "Authorization: Bearer 12345" \\
  -k \\
  --connect-timeout 10 \\
  --max-time 30 \\
  https://hitsheubgqzb.ap-southeast-1.clawcloudrun.com/v1/models
'''
    print(curl_examples)

def main():
    """主函数"""
    print("🚀 Gemini Balance OpenAI SDK 测试")
    print("=" * 50)
    
    if not SDK_AVAILABLE:
        print("请先安装 OpenAI SDK:")
        print("pip install openai")
        return
    
    # 运行各种测试
    test_with_openai_sdk()
    test_code_generation()
    test_conversation()
    show_curl_examples()
    
    print("\n" + "=" * 50)
    print("📝 总结和建议:")
    print("1. OpenAI SDK 通常比直接使用 requests 更稳定")
    print("2. SDK 自动处理重试、超时等问题")
    print("3. 如果 SDK 也失败，可能是服务器端问题")
    print("4. 可以尝试不同的模型，有些可能更稳定")
    print("5. 检查网络环境和防火墙设置")

if __name__ == "__main__":
    main()
