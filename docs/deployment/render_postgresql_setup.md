# 🎯 Render PostgreSQL 设置指南

## 为什么选择 Render PostgreSQL？

- 🆓 **免费版**：90天试用
- 💰 **$7/月**：生产环境
- ✅ **同一平台**：与应用在同一个地方
- ✅ **零网络问题**：内网连接
- ✅ **自动备份**：数据安全

## 🚀 设置步骤

### 1. 在 Render 创建 PostgreSQL
1. 登录 Render 控制台
2. 点击 "New" → "PostgreSQL"
3. 填写信息：
   - Name: `gemini-balance-db`
   - Database: `gemini_balance`
   - User: `gemini_user`
4. 选择免费计划
5. 点击 "Create Database"

### 2. 获取连接信息
创建完成后，Render 会提供：
- Internal Database URL（内网连接，推荐）
- External Database URL（外网连接）

### 3. 修改 gemini-balance 支持 PostgreSQL
需要添加 PostgreSQL 驱动和配置

### 4. 环境变量配置
```env
DATABASE_TYPE=postgresql
DATABASE_URL=<render-internal-url>
```

## 💰 成本对比

| 计划 | 价格 | 存储 | 连接数 | 备份 |
|------|------|------|--------|------|
| 免费 | $0 (90天) | 1GB | 97 | 7天 |
| 付费 | $7/月 | 10GB | 97 | 7天 |

## 🔧 代码修改

需要修改 gemini-balance 以支持 PostgreSQL：
1. 添加 PostgreSQL 驱动
2. 更新数据库连接配置
3. 处理 SQL 语法差异

## 🎯 优势

1. **最简单**：同一平台管理
2. **最稳定**：内网连接
3. **最安全**：无需暴露端口
4. **PostgreSQL**：性能通常比 MySQL 更好
