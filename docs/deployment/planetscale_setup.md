# 🌟 PlanetScale MySQL 部署指南

## 为什么选择 PlanetScale？

- ✅ **免费额度充足**：5GB 存储，足够小项目使用
- ✅ **MySQL 兼容**：无需修改 gemini-balance 代码
- ✅ **全球 CDN**：Render 访问速度快
- ✅ **无需维护**：自动备份、扩展、监控
- ✅ **简单易用**：几分钟完成设置

## 🚀 设置步骤

### 1. 注册 PlanetScale 账户
访问：https://planetscale.com
- 使用 GitHub 账户登录（推荐）
- 或邮箱注册

### 2. 创建数据库
```
Database name: gemini-balance
Region: us-east (靠近 Render 服务器)
```

### 3. 获取连接信息
在 PlanetScale 控制台：
- 点击 "Connect"
- 选择 "General" 或 "Python"
- 复制连接字符串

### 4. 在 Render 中配置环境变量
```
DATABASE_TYPE=mysql
MYSQL_HOST=<planetscale-host>
MYSQL_PORT=3306
MYSQL_USER=<planetscale-user>
MYSQL_PASSWORD=<planetscale-password>
MYSQL_DATABASE=gemini-balance
```

### 5. 部署 gemini-balance
直接部署，PlanetScale 会自动创建表结构

## 💰 成本对比

| 服务 | 免费额度 | 付费价格 | 优点 |
|------|---------|---------|------|
| PlanetScale | 5GB | $29/月 | 最简单，MySQL兼容 |
| Railway | 无 | $5/月 | 便宜，简单 |
| Vultr VPS | 无 | $2.5/月 | 最便宜，完全控制 |
| 阿里云 RDS | 无 | ¥10/月 | 国内快，中文支持 |

## 🎯 我的推荐

**对于 gemini-balance 项目**：
1. **开发/测试**：PlanetScale 免费版
2. **生产环境**：Railway ($5/月) 或 Vultr VPS ($2.5/月)
3. **国内用户**：阿里云 RDS

你想选择哪个方案？我可以帮你详细设置！
