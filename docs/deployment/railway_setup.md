# 🚂 Railway MySQL 部署指南

## 为什么选择 Railway？

- 🆓 **$5 免费额度/月**：足够小项目使用
- ✅ **简单易用**：几分钟完成设置
- ✅ **MySQL 8.0**：完全兼容
- ✅ **与 Render 兼容**：同样的云原生架构
- 💰 **按使用量付费**：用多少付多少

## 🚀 设置步骤

### 1. 注册 Railway 账户
- 访问：https://railway.app
- 使用 GitHub 账户登录（推荐）

### 2. 创建 MySQL 数据库
1. 点击 "New Project"
2. 选择 "Provision MySQL"
3. 等待部署完成（约1-2分钟）

### 3. 获取连接信息
1. 点击 MySQL 服务
2. 进入 "Variables" 标签
3. 复制以下信息：
   - `MYSQL_HOST`
   - `MYSQL_PORT`
   - `MYSQL_USER`
   - `MYSQL_PASSWORD`
   - `MYSQL_DATABASE`

### 4. 在 Render 中配置
```env
DATABASE_TYPE=mysql
MYSQL_HOST=<railway-host>
MYSQL_PORT=<railway-port>
MYSQL_USER=<railway-user>
MYSQL_PASSWORD=<railway-password>
MYSQL_DATABASE=<railway-database>
```

## 💰 成本估算

**免费额度**：$5/月
- 小型项目通常用不完
- 包含计算时间和存储

**超出后**：按使用量付费
- 存储：$0.25/GB/月
- 计算：$0.000463/GB-hour

**实际成本**：小项目通常 $0-2/月

## 🎯 优势

1. **真正免费开始**
2. **简单设置**
3. **自动扩展**
4. **内置监控**
5. **与现代开发工具集成**
