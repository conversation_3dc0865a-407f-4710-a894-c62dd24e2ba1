# 🌐 腾讯云 MySQL 设置指南

## 💰 价格对比

| 配置 | 规格 | 价格/月 | 适用场景 |
|------|------|---------|----------|
| 基础版 | 1核1GB | ¥8-12 | 开发测试 |
| 基础版 | 1核2GB | ¥15-20 | 小型项目 |
| 高可用版 | 2核4GB | ¥50+ | 生产环境 |

## 🚀 购买步骤

### 1. 访问腾讯云控制台
- 网址：https://console.cloud.tencent.com/cdb
- 登录腾讯云账户

### 2. 购买 MySQL 实例
1. 点击"新建"
2. 选择配置：
   - **地域**：北京/上海/广州（选择离你近的）
   - **数据库版本**：MySQL 8.0
   - **架构**：基础版
   - **实例规格**：1核1GB内存
   - **硬盘**：20GB SSD
   - **网络**：私有网络VPC
   - **安全组**：默认安全组
3. 设置实例名称：`gemini-balance-mysql`
4. 设置 root 密码：`Strong_Root_Password_123!`
5. 确认购买

### 3. 配置网络访问
购买完成后：
1. 进入实例详情
2. 点击"安全组"标签
3. 配置安全组规则：
   - 类型：MySQL(3306)
   - 来源：0.0.0.0/0（允许所有IP访问）
   - 策略：允许

### 4. 获取连接信息
在实例详情页面获取：
- **内网地址**：用于腾讯云内部访问
- **外网地址**：用于 Render 访问（需要开启）

### 5. 开启外网访问
1. 在实例详情页面
2. 找到"外网地址"
3. 点击"开启"
4. 记录外网地址和端口

## 📋 Render 配置

获取腾讯云 MySQL 信息后，在 Render 中配置：

```env
DATABASE_TYPE=mysql
MYSQL_HOST=<腾讯云外网地址>
MYSQL_PORT=3306
MYSQL_USER=root
MYSQL_PASSWORD=Strong_Root_Password_123!
MYSQL_DATABASE=gemini_balance
```

## 🔧 数据库初始化

连接到腾讯云 MySQL 后，创建数据库和用户：

```sql
-- 创建数据库
CREATE DATABASE gemini_balance CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建专用用户
CREATE USER 'render_user'@'%' IDENTIFIED BY 'Strong_Password_123!';

-- 授予权限
GRANT ALL PRIVILEGES ON gemini_balance.* TO 'render_user'@'%';

-- 刷新权限
FLUSH PRIVILEGES;
```

## 💡 优化建议

### 安全设置：
1. **修改默认端口**：从3306改为其他端口
2. **限制访问IP**：只允许 Render 的IP访问
3. **定期备份**：开启自动备份
4. **监控告警**：设置资源使用告警

### 性能优化：
1. **选择合适地域**：选择离 Render 服务器近的地域
2. **升级配置**：根据使用情况升级规格
3. **连接池**：在应用中配置连接池

## 🎁 新用户优惠

腾讯云新用户通常有：
- **首月免费**或大幅折扣
- **代金券**：可用于购买云服务
- **长期优惠**：年付有额外折扣

## 📞 技术支持

- **文档**：https://cloud.tencent.com/document/product/236
- **工单系统**：24小时中文技术支持
- **社区论坛**：开发者交流
