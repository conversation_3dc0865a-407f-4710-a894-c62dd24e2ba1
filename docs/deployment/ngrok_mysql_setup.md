# 🚇 ngrok + Docker MySQL 设置指南

## 📋 当前状态
- ✅ ngrok 已安装 (v3.25.1)
- ✅ MySQL 容器运行中 (端口 443)
- ⚠️ 需要配置 ngrok 认证

## 🚀 设置步骤

### 1. 注册 ngrok 账户
1. 访问：https://ngrok.com/signup
2. 注册免费账户（可以用 GitHub 登录）
3. 验证邮箱

### 2. 获取 authtoken
1. 登录 ngrok 控制台：https://dashboard.ngrok.com
2. 进入 "Your Authtoken" 页面
3. 复制 authtoken

### 3. 配置 ngrok
```bash
ngrok config add-authtoken <your-authtoken>
```

### 4. 启动 TCP 隧道
```bash
ngrok tcp 443
```

### 5. 获取隧道地址
ngrok 启动后会显示类似：
```
Forwarding    tcp://0.tcp.ngrok.io:12345 -> localhost:443
```

### 6. 在 Render 中配置
```env
DATABASE_TYPE=mysql
MYSQL_HOST=0.tcp.ngrok.io
MYSQL_PORT=12345
MYSQL_USER=render_user
MYSQL_PASSWORD=Strong_Password_123!
MYSQL_DATABASE=gemini_balance
```

## 💰 ngrok 费用说明

### 免费计划限制：
- ❌ **TCP 隧道需要付费**
- ✅ HTTP 隧道免费
- ✅ 1 个在线隧道

### 付费计划：
- 💰 **Personal**: $8/月
  - ✅ TCP 隧道
  - ✅ 自定义域名
  - ✅ 更多并发隧道

## 🔄 替代方案

如果不想付费，可以考虑：

### 1. HTTP 隧道 + MySQL 代理
使用免费的 HTTP 隧道 + 自建 MySQL HTTP 代理

### 2. 其他隧道服务
- **frp**: 开源，需要自己的服务器
- **localtunnel**: 免费，但不稳定
- **serveo**: 免费 SSH 隧道

### 3. 云数据库
- **Railway**: $5 免费额度
- **Aiven**: 30天试用
- **Render PostgreSQL**: $7/月

## 🎯 推荐

1. **如果愿意付费 $8/月**: 使用 ngrok TCP 隧道
2. **如果要免费**: 使用 Railway 或 Render PostgreSQL
3. **如果要折中**: 使用 HTTP 隧道 + MySQL 代理
